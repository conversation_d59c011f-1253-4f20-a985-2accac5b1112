// Main App Controller
class ElectricityApp {
    constructor() {
        this.currentPage = 'dashboard';
        this.isInitialized = false;
        this.init();
    }

    async init() {
        try {
            console.log('📱 Starting app initialization...');

            // Show loading screen
            this.showLoading();

            // Initialize storage
            console.log('💾 Initializing storage...');
            if (typeof StorageManager === 'undefined') {
                throw new Error('StorageManager is not available. Please check if storage.js is loaded.');
            }
            await StorageManager.init();

            // Check if app needs initial setup
            if (!StorageManager.isInitialized()) {
                console.log('🔧 First time setup...');
                await this.showInitialSetup();
            }

            // Initialize all modules
            console.log('🔌 Initializing modules...');
            await this.initializeModules();

            // Setup event listeners
            console.log('👂 Setting up event listeners...');
            this.setupEventListeners();

            // Hide loading screen and show app
            console.log('🎨 Showing app interface...');
            this.hideLoading();

            // Load initial page
            this.showPage('dashboard');

            this.isInitialized = true;
            console.log('✅ Electricity App initialized successfully');

        } catch (error) {
            console.error('❌ Failed to initialize app:', error);
            this.showError(`Failed to initialize app: ${error.message}. Please refresh the page.`);

            // Try to show a basic error interface
            const app = document.getElementById('app');
            if (app) {
                app.style.display = 'block';
                app.innerHTML = `
                    <div style="padding: 20px; text-align: center; background: #f44336; color: white;">
                        <h2>⚠️ App Error</h2>
                        <p>The app failed to start properly.</p>
                        <p style="font-size: 14px; margin: 10px 0;">Error: ${error.message}</p>
                        <button onclick="location.reload()" style="background: white; color: #f44336; border: none; padding: 10px 20px; border-radius: 5px; margin: 10px; cursor: pointer;">
                            🔄 Reload App
                        </button>
                        <button onclick="window.open('debug.html')" style="background: rgba(255,255,255,0.2); color: white; border: 1px solid white; padding: 10px 20px; border-radius: 5px; margin: 10px; cursor: pointer;">
                            🔧 Debug Mode
                        </button>
                    </div>
                `;
            }
        }
    }

    async initializeModules() {
        // Initialize all app modules with error handling
        try {
            console.log('🎨 Initializing Theme Manager...');
            if (typeof ThemeManager !== 'undefined') {
                window.themeManager = new ThemeManager();
                await window.themeManager.init();
            } else {
                console.warn('⚠️ ThemeManager not available');
            }

            console.log('📊 Initializing Dashboard Manager...');
            if (typeof DashboardManager !== 'undefined') {
                window.dashboardManager = new DashboardManager();
            } else {
                console.warn('⚠️ DashboardManager not available');
            }

            console.log('💰 Initializing Purchases Manager...');
            if (typeof PurchasesManager !== 'undefined') {
                window.purchasesManager = new PurchasesManager();
            } else {
                console.warn('⚠️ PurchasesManager not available');
            }

            console.log('📈 Initializing Usage Manager...');
            if (typeof UsageManager !== 'undefined') {
                window.usageManager = new UsageManager();
            } else {
                console.warn('⚠️ UsageManager not available');
            }

            console.log('📋 Initializing History Manager...');
            if (typeof HistoryManager !== 'undefined') {
                window.historyManager = new HistoryManager();
            } else {
                console.warn('⚠️ HistoryManager not available');
            }

            console.log('⚙️ Initializing Settings Manager...');
            if (typeof SettingsManager !== 'undefined') {
                window.settingsManager = new SettingsManager();
            } else {
                console.warn('⚠️ SettingsManager not available');
            }

            console.log('🔔 Initializing Notification Manager...');
            if (typeof NotificationManager !== 'undefined') {
                window.notificationManager = new NotificationManager();
            } else {
                console.warn('⚠️ NotificationManager not available');
            }

            console.log('✅ All available modules initialized');
        } catch (error) {
            console.error('❌ Error initializing modules:', error);
            throw error;
        }
    }

    setupEventListeners() {
        // Menu toggle
        const menuToggle = document.getElementById('menu-toggle');
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebar-overlay');
        
        menuToggle?.addEventListener('click', () => this.toggleSidebar());
        sidebarOverlay?.addEventListener('click', () => this.closeSidebar());
        
        // Navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.dataset.page;
                if (page) {
                    this.showPage(page);
                    this.closeSidebar();
                }
            });
        });
        
        // Quick action buttons
        document.querySelectorAll('.quick-action-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = btn.dataset.action;
                this.handleQuickAction(action);
            });
        });
        
        // Theme toggle
        const themeToggle = document.getElementById('theme-toggle');
        themeToggle?.addEventListener('click', () => {
            if (window.themeManager) {
                window.themeManager.toggleTheme();
            }
        });
        
        // Handle back button
        window.addEventListener('popstate', (e) => {
            const page = e.state?.page || 'dashboard';
            this.showPage(page, false);
        });
        
        // Handle app visibility change
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.isInitialized) {
                this.refreshCurrentPage();
            }
        });
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        
        sidebar?.classList.toggle('open');
        overlay?.classList.toggle('active');
    }

    closeSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        
        sidebar?.classList.remove('open');
        overlay?.classList.remove('active');
    }

    showPage(pageName, updateHistory = true) {
        // Hide all pages
        document.querySelectorAll('.page').forEach(page => {
            page.classList.remove('active');
        });
        
        // Show target page
        const targetPage = document.getElementById(`${pageName}-page`);
        if (targetPage) {
            targetPage.classList.add('active');
            this.currentPage = pageName;
            
            // Update navigation
            this.updateNavigation(pageName);
            
            // Update browser history
            if (updateHistory) {
                history.pushState({ page: pageName }, '', `#${pageName}`);
            }
            
            // Refresh page content
            this.refreshPage(pageName);
        }
    }

    updateNavigation(activePage) {
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
            if (link.dataset.page === activePage) {
                link.classList.add('active');
            }
        });
    }

    refreshPage(pageName) {
        switch (pageName) {
            case 'dashboard':
                if (window.dashboardManager) {
                    window.dashboardManager.refresh();
                }
                break;
            case 'purchases':
                if (window.purchasesManager) {
                    window.purchasesManager.refresh();
                }
                break;
            case 'usage':
                if (window.usageManager) {
                    window.usageManager.refresh();
                }
                break;
            case 'history':
                if (window.historyManager) {
                    window.historyManager.refresh();
                }
                break;
            case 'settings':
                if (window.settingsManager) {
                    window.settingsManager.refresh();
                }
                break;
        }
    }

    refreshCurrentPage() {
        this.refreshPage(this.currentPage);
    }

    handleQuickAction(action) {
        switch (action) {
            case 'add-purchase':
                this.showPage('purchases');
                break;
            case 'record-usage':
                this.showPage('usage');
                break;
            case 'view-history':
                this.showPage('history');
                break;
        }
    }

    showLoading() {
        const loadingScreen = document.getElementById('loading-screen');
        const app = document.getElementById('app');
        
        if (loadingScreen) loadingScreen.style.display = 'flex';
        if (app) app.style.display = 'none';
    }

    hideLoading() {
        const loadingScreen = document.getElementById('loading-screen');
        const app = document.getElementById('app');
        
        setTimeout(() => {
            if (loadingScreen) {
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                    if (app) app.style.display = 'flex';
                }, 300);
            }
        }, 1000); // Show loading for at least 1 second
    }

    async showInitialSetup() {
        console.log('🔧 Showing initial setup modal...');

        return new Promise((resolve) => {
            // Create initial setup modal
            const modal = document.createElement('div');
            modal.id = 'initial-setup-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                backdrop-filter: blur(10px);
            `;

            modal.innerHTML = `
                <div style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    padding: 40px;
                    border-radius: 20px;
                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
                    color: white;
                    text-align: center;
                    max-width: 500px;
                    width: 90%;
                    max-height: 90vh;
                    overflow-y: auto;
                ">
                    <div style="font-size: 3rem; margin-bottom: 20px;">⚡</div>
                    <h2 style="margin-bottom: 10px; font-size: 1.8rem;">Welcome to PREPAID USER - ELECTRICITY</h2>
                    <p style="margin-bottom: 30px; opacity: 0.9; line-height: 1.5;">
                        To get started, please enter your current electricity units and initial purchase information.
                    </p>

                    <div style="text-align: left; margin-bottom: 30px;">
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">Current Units on Meter:</label>
                            <input type="number" id="initial-units" step="0.1" min="0" placeholder="e.g., 150.5"
                                style="width: 100%; padding: 12px; border: none; border-radius: 8px; font-size: 16px; box-sizing: border-box;">
                            <small style="opacity: 0.8; font-size: 12px;">Enter the current reading from your electricity meter</small>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">Initial Purchase Amount:</label>
                            <input type="number" id="initial-amount" step="0.01" min="0" placeholder="e.g., 25.00"
                                style="width: 100%; padding: 12px; border: none; border-radius: 8px; font-size: 16px; box-sizing: border-box;">
                            <small style="opacity: 0.8; font-size: 12px;">Amount you paid for your current units (optional)</small>
                        </div>
                    </div>

                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button id="setup-complete-btn" style="
                            background: rgba(255, 255, 255, 0.2);
                            color: white;
                            border: 2px solid white;
                            padding: 12px 24px;
                            border-radius: 8px;
                            cursor: pointer;
                            font-weight: 600;
                            font-size: 16px;
                            transition: all 0.3s ease;
                        " onmouseover="this.style.background='white'; this.style.color='#667eea'"
                           onmouseout="this.style.background='rgba(255, 255, 255, 0.2)'; this.style.color='white'">
                            ✅ Complete Setup
                        </button>
                        <button id="setup-skip-btn" style="
                            background: transparent;
                            color: rgba(255, 255, 255, 0.7);
                            border: 1px solid rgba(255, 255, 255, 0.3);
                            padding: 12px 24px;
                            border-radius: 8px;
                            cursor: pointer;
                            font-size: 16px;
                        ">
                            Skip for Now
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Focus on first input
            setTimeout(() => {
                document.getElementById('initial-units').focus();
            }, 100);

            // Handle setup completion
            document.getElementById('setup-complete-btn').addEventListener('click', () => {
                const units = parseFloat(document.getElementById('initial-units').value) || 0;
                const amount = parseFloat(document.getElementById('initial-amount').value) || 0;

                if (units <= 0) {
                    alert('Please enter a valid current units value greater than 0.');
                    return;
                }

                // Set initial values
                StorageManager.setCurrentUnits(units);

                if (amount > 0) {
                    // Add initial purchase record
                    const purchase = {
                        id: Date.now(),
                        date: new Date().toISOString(),
                        amount: amount,
                        units: units,
                        costPerUnit: amount / units,
                        type: 'initial'
                    };
                    StorageManager.addPurchase(purchase);
                }

                // Mark as initialized
                StorageManager.setInitialized(true);

                // Remove modal
                document.body.removeChild(modal);

                console.log('✅ Initial setup completed:', { units, amount });
                resolve();
            });

            // Handle skip
            document.getElementById('setup-skip-btn').addEventListener('click', () => {
                // Set minimal initial values
                StorageManager.setCurrentUnits(0);
                StorageManager.setInitialized(true);

                // Remove modal
                document.body.removeChild(modal);

                console.log('⏭️ Initial setup skipped');
                resolve();
            });
        });
    }

    showError(message) {
        // Simple error display - can be enhanced later
        alert(message);
    }

    // Utility methods
    formatCurrency(amount, currency = null) {
        const settings = StorageManager.getSettings();
        const currencySymbol = currency || settings.currency.symbol;
        return `${currencySymbol}${amount.toFixed(2)}`;
    }

    formatUnits(amount, unit = null) {
        const settings = StorageManager.getSettings();
        const unitName = unit || settings.unit.name;
        return `${amount} ${unitName}`;
    }

    showNotification(message, type = 'info') {
        // Simple notification - can be enhanced later
        console.log(`${type.toUpperCase()}: ${message}`);
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    try {
        console.log('🚀 Initializing PREPAID USER - ELECTRICITY App...');
        window.electricityApp = new ElectricityApp();
        console.log('✅ App initialized successfully');
    } catch (error) {
        console.error('❌ Failed to initialize app:', error);

        // Show error message to user
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.innerHTML = `
                <div style="text-align: center; color: white; padding: 40px;">
                    <div style="font-size: 3rem; margin-bottom: 20px;">⚠️</div>
                    <h2>App Failed to Start</h2>
                    <p style="margin: 20px 0;">There was an error starting the application.</p>
                    <p style="font-size: 14px; opacity: 0.8;">Error: ${error.message}</p>
                    <button onclick="location.reload()" style="background: white; color: #2196F3; border: none; padding: 10px 20px; border-radius: 5px; margin-top: 20px; cursor: pointer;">
                        🔄 Retry
                    </button>
                    <button onclick="window.open('debug.html')" style="background: rgba(255,255,255,0.2); color: white; border: 1px solid white; padding: 10px 20px; border-radius: 5px; margin: 20px 10px; cursor: pointer;">
                        🔧 Debug Mode
                    </button>
                </div>
            `;
        }
    }
});

// Service Worker registration for PWA functionality
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}
