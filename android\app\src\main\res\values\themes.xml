<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.PrepaidElectricity" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/accent_primary</item>
        <item name="colorPrimaryVariant">@color/accent_secondary</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor" tools:targetApi="l">@color/status_bar</item>
        <!-- Background colors -->
        <item name="android:windowBackground">@color/background_primary</item>
        <item name="colorSurface">@color/background_secondary</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <!-- Navigation bar -->
        <item name="android:navigationBarColor">@color/background_primary</item>
        <!-- Window flags -->
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
    </style>

    <!-- No Action Bar Theme -->
    <style name="Theme.PrepaidElectricity.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!-- Splash Screen Theme -->
    <style name="Theme.PrepaidElectricity.Splash" parent="Theme.PrepaidElectricity.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
</resources>
