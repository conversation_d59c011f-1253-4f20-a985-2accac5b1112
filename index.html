<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PREPAID USER - ELECTRICITY</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="manifest" href="public/manifest.json">
    <meta name="theme-color" content="#2196F3">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Prepaid Electricity">
    <!-- Icons will be added when available -->
    <!-- <link rel="apple-touch-icon" href="assets/icon-192.png"> -->
    <!-- <link rel="icon" type="image/png" sizes="192x192" href="assets/icon-192.png"> -->
    <!-- <link rel="icon" type="image/png" sizes="512x512" href="assets/icon-512.png"> -->
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="logo-container">
                <svg class="lightning-logo" width="48" height="48" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="loadingLightningGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
                            <stop offset="50%" style="stop-color:#FFA500;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#FF6B35;stop-opacity:1" />
                        </linearGradient>
                        <filter id="loadingGlow">
                            <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
                            <feMerge>
                                <feMergeNode in="coloredBlur"/>
                                <feMergeNode in="SourceGraphic"/>
                            </feMerge>
                        </filter>
                    </defs>
                    <path d="M18 2L8 16H14L10 30L22 16H16L18 2Z"
                          fill="url(#loadingLightningGradient)"
                          stroke="#FFD700"
                          stroke-width="1"
                          filter="url(#loadingGlow)"/>
                </svg>
                <h2>PREPAID USER</h2>
                <p>ELECTRICITY</p>
            </div>
            <div class="loading-spinner"></div>
        </div>
    </div>

    <!-- Main App Container -->
    <div id="app" class="app-container" style="display: none;">
        <!-- Header -->
        <header class="app-header">
            <button id="menu-toggle" class="menu-toggle" aria-label="Toggle menu">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <h1 class="app-title">
                <svg class="lightning-icon" width="28" height="28" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="lightningGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
                            <stop offset="50%" style="stop-color:#FFA500;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#FF6B35;stop-opacity:1" />
                        </linearGradient>
                        <filter id="glow">
                            <feGaussianBlur stdDeviation="1.5" result="coloredBlur"/>
                            <feMerge>
                                <feMergeNode in="coloredBlur"/>
                                <feMergeNode in="SourceGraphic"/>
                            </feMerge>
                        </filter>
                    </defs>
                    <path d="M18 2L8 16H14L10 30L22 16H16L18 2Z"
                          fill="url(#lightningGradient)"
                          stroke="#FFD700"
                          stroke-width="0.5"
                          filter="url(#glow)"/>
                </svg>
                PREPAID USER
            </h1>
            <div class="header-actions">
                <button id="theme-toggle" class="icon-btn" aria-label="Toggle theme">
                    <span class="theme-icon">🌙</span>
                </button>
            </div>
        </header>

        <!-- Sidebar Navigation -->
        <nav id="sidebar" class="sidebar">
            <div class="sidebar-header">
                <div class="logo-container">
                    <svg class="lightning-logo" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="sidebarLightningGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
                                <stop offset="50%" style="stop-color:#FFA500;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#FF6B35;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <path d="M18 2L8 16H14L10 30L22 16H16L18 2Z"
                              fill="url(#sidebarLightningGradient)"
                              stroke="#FFD700"
                              stroke-width="0.5"/>
                    </svg>
                    <div class="logo-text">
                        <h3>PREPAID USER</h3>
                        <p>ELECTRICITY</p>
                    </div>
                </div>
            </div>
            <ul class="nav-menu">
                <li><a href="#dashboard" class="nav-link active" data-page="dashboard">
                    <span class="nav-icon">📊</span>Dashboard
                </a></li>
                <li><a href="#purchases" class="nav-link" data-page="purchases">
                    <span class="nav-icon">💰</span>Purchases
                </a></li>
                <li><a href="#usage" class="nav-link" data-page="usage">
                    <span class="nav-icon">📈</span>Usage
                </a></li>
                <li><a href="#history" class="nav-link" data-page="history">
                    <span class="nav-icon">📋</span>History
                </a></li>
                <li><a href="#settings" class="nav-link" data-page="settings">
                    <span class="nav-icon">⚙️</span>Settings
                </a></li>
            </ul>
        </nav>

        <!-- Sidebar Overlay -->
        <div id="sidebar-overlay" class="sidebar-overlay"></div>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Page -->
            <div id="dashboard-page" class="page active">
                <div class="page-header">
                    <h2><span class="page-icon">📊</span>Dashboard</h2>
                </div>
                
                <!-- Quick Actions - 3 Vertical Buttons -->
                <div class="quick-actions-vertical">
                    <button class="quick-action-btn-vertical" onclick="window.electricityApp.showPage('purchases')">
                        <span class="action-icon">⚡</span>
                        <span class="action-text">Add Purchase</span>
                    </button>
                    <button class="quick-action-btn-vertical" onclick="window.electricityApp.showPage('usage')">
                        <span class="action-icon">📊</span>
                        <span class="action-text">Record Usage</span>
                    </button>
                    <button class="quick-action-btn-vertical" onclick="window.electricityApp.showPage('history')">
                        <span class="action-icon">📋</span>
                        <span class="action-text">View History</span>
                    </button>
                </div>

                <!-- Current Status Cards -->
                <div class="status-cards">
                    <div class="status-card">
                        <div class="card-header">
                            <span class="card-icon">⚡</span>
                            <h3>Current Units</h3>
                        </div>
                        <div class="card-value" id="current-units">0</div>
                        <div class="card-unit" id="current-units-label">Units</div>
                    </div>
                    
                    <div class="status-card">
                        <div class="card-header">
                            <span class="card-icon">📊</span>
                            <h3>Usage Since Last</h3>
                        </div>
                        <div class="card-value" id="usage-since-last">0</div>
                        <div class="card-unit" id="usage-units-label">Units</div>
                    </div>
                </div>

                <!-- Modern Gradient Usage Dial -->
                <div class="dial-container">
                    <div class="dial-wrapper">
                        <canvas id="usage-dial" width="320" height="320"></canvas>
                        <div class="dial-center">
                            <div class="dial-units" id="dial-units">0.0 Units</div>
                            <div class="dial-percentage" id="dial-percentage">0%</div>
                            <div class="dial-status" id="dial-status">GOOD</div>
                        </div>
                    </div>
                </div>

                <!-- Usage Summary -->
                <div class="usage-summary">
                    <div class="summary-card">
                        <h4><span class="summary-icon">📅</span>Weekly Usage</h4>
                        <div class="summary-value">
                            <span id="weekly-units">0</span> <span id="weekly-units-label">Units</span>
                        </div>
                        <div class="summary-cost" id="weekly-cost">$0.00</div>
                    </div>
                    
                    <div class="summary-card">
                        <h4><span class="summary-icon">📆</span>Monthly Usage</h4>
                        <div class="summary-value">
                            <span id="monthly-units">0</span> <span id="monthly-units-label">Units</span>
                        </div>
                        <div class="summary-cost" id="monthly-cost">$0.00</div>
                    </div>
                </div>

                <!-- Low Units Warning -->
                <div id="low-units-warning" class="warning-card" style="display: none;">
                    <div class="warning-icon">⚠️</div>
                    <div class="warning-content">
                        <h4>Low Units Warning</h4>
                        <p>Your electricity units are running low. Consider purchasing more units soon.</p>
                    </div>
                </div>
            </div>

            <!-- Purchases Page -->
            <div id="purchases-page" class="page">
                <div class="page-header">
                    <h2><span class="page-icon">💰</span>Purchases</h2>
                </div>

                <div class="purchase-form-container">
                    <div class="form-card">
                        <h3>Add New Purchase</h3>
                        <form id="purchase-form">
                            <div class="form-group">
                                <label for="purchase-amount">Amount Spent</label>
                                <div class="input-with-currency">
                                    <span class="currency-symbol" id="purchase-currency-symbol">$</span>
                                    <input type="number" id="purchase-amount" step="0.01" min="0" placeholder="0.00" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="purchase-units">Units Purchased</label>
                                <input type="number" id="purchase-units" step="0.1" min="0" placeholder="0" required>
                                <span class="unit-label" id="purchase-unit-label">Units</span>
                            </div>

                            <div class="purchase-preview" id="purchase-preview">
                                <div class="preview-item">
                                    <span>Cost per unit:</span>
                                    <span id="preview-cost-per-unit">$0.00</span>
                                </div>
                                <div class="preview-item">
                                    <span>Current units:</span>
                                    <span id="preview-current-units">0</span>
                                </div>
                                <div class="preview-item">
                                    <span>New total:</span>
                                    <span id="preview-new-total">0</span>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <span class="btn-icon">💰</span>
                                Add Purchase
                            </button>
                        </form>
                    </div>
                </div>

                <div class="recent-purchases">
                    <h3>Recent Purchases</h3>
                    <div id="purchases-list" class="purchases-list">
                        <!-- Purchases will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Usage Page -->
            <div id="usage-page" class="page">
                <div class="page-header">
                    <h2><span class="page-icon">📈</span>Usage</h2>
                </div>

                <div class="usage-form-container">
                    <div class="form-card">
                        <h3>Record Usage</h3>
                        <form id="usage-form">
                            <div class="current-units-display">
                                <div class="units-info">
                                    <span class="label">Current Units:</span>
                                    <span class="value" id="current-units-display">0</span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="new-units">New Unit Reading</label>
                                <input type="number" id="new-units" step="0.1" min="0" placeholder="Enter current meter reading" required>
                                <span class="unit-label" id="usage-unit-label">Units</span>
                            </div>

                            <div class="usage-preview" id="usage-preview">
                                <div class="preview-item">
                                    <span>Units used:</span>
                                    <span id="preview-usage">0</span>
                                </div>
                                <div class="preview-item">
                                    <span>Estimated cost:</span>
                                    <span id="preview-cost">$0.00</span>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <span class="btn-icon">📈</span>
                                Record Usage
                            </button>
                        </form>
                    </div>
                </div>

                <div class="usage-chart-container">
                    <h3>Usage Chart</h3>
                    <canvas id="usage-chart" width="400" height="200"></canvas>
                </div>

                <div class="recent-usage">
                    <h3>Recent Usage</h3>
                    <div id="usage-list" class="usage-list">
                        <!-- Usage records will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- History Page -->
            <div id="history-page" class="page">
                <div class="page-header">
                    <h2><span class="page-icon">📋</span>History</h2>
                </div>

                <div class="history-summary">
                    <div class="summary-cards">
                        <div class="summary-card">
                            <div class="card-icon">📊</div>
                            <div class="card-content">
                                <div class="card-title">Weekly Usage</div>
                                <div class="card-value" id="weekly-usage">0 Units</div>
                                <div class="card-cost" id="weekly-cost">$0.00</div>
                            </div>
                        </div>

                        <div class="summary-card">
                            <div class="card-icon">📈</div>
                            <div class="card-content">
                                <div class="card-title">Monthly Usage</div>
                                <div class="card-value" id="monthly-usage">0 Units</div>
                                <div class="card-cost" id="monthly-cost">$0.00</div>
                            </div>
                        </div>

                        <div class="summary-card">
                            <div class="card-icon">💰</div>
                            <div class="card-content">
                                <div class="card-title">Total Purchases</div>
                                <div class="card-value" id="total-purchases">0</div>
                                <div class="card-cost" id="total-spent">$0.00</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="history-filters">
                    <div class="filter-tabs">
                        <button class="filter-tab active" data-filter="all">All</button>
                        <button class="filter-tab" data-filter="purchases">Purchases</button>
                        <button class="filter-tab" data-filter="usage">Usage</button>
                    </div>
                </div>

                <div class="history-list-container">
                    <div id="history-list" class="history-list">
                        <!-- History items will be loaded here -->
                    </div>
                </div>

                <div class="history-actions">
                    <button class="btn btn-secondary" id="export-data-btn">
                        <span class="btn-icon">📤</span>
                        Export Data
                    </button>
                    <button class="btn btn-secondary" id="import-data-btn">
                        <span class="btn-icon">📥</span>
                        Import Data
                    </button>
                </div>
            </div>

            <!-- Settings Page -->
            <div id="settings-page" class="page">
                <div class="page-header">
                    <h2><span class="page-icon">⚙️</span>Settings</h2>
                </div>

                <div class="settings-container">
                    <!-- General Settings -->
                    <div class="settings-section">
                        <h3><span class="section-icon">⚙️</span>General Settings</h3>

                        <div class="setting-item">
                            <label for="currency-select">💰 Currency</label>
                            <select id="currency-select">
                                <option value="USD">USD ($) - US Dollar</option>
                                <option value="EUR">EUR (€) - Euro</option>
                                <option value="GBP">GBP (£) - British Pound</option>
                                <option value="ZAR">ZAR (R) - South African Rand</option>
                                <option value="AUD">AUD (A$) - Australian Dollar</option>
                                <option value="CUSTOM">Custom Currency</option>
                            </select>
                        </div>

                        <div class="setting-item" id="custom-currency-container" style="display: none;">
                            <label for="custom-currency-name">Custom Currency Name</label>
                            <input type="text" id="custom-currency-name" placeholder="e.g., Bitcoin, Peso" maxlength="20">
                        </div>

                        <div class="setting-item" id="custom-currency-symbol-container" style="display: none;">
                            <label for="custom-currency-symbol">Custom Currency Symbol</label>
                            <input type="text" id="custom-currency-symbol" placeholder="e.g., ₿, ₱" maxlength="3">
                        </div>

                        <div class="setting-item">
                            <label for="unit-select">⚡ Unit Type</label>
                            <select id="unit-select">
                                <option value="Units">Units (Generic electricity units)</option>
                                <option value="KWh">KWh (Kilowatt hours)</option>
                                <option value="CUSTOM">Custom Unit Type</option>
                            </select>
                        </div>

                        <div class="setting-item" id="custom-unit-container" style="display: none;">
                            <label for="custom-unit-name">Custom Unit Name</label>
                            <input type="text" id="custom-unit-name" placeholder="e.g., Credits, Points" maxlength="15">
                        </div>

                        <div class="setting-item">
                            <label for="cost-per-unit">Cost per Unit</label>
                            <div class="input-with-currency">
                                <span class="currency-symbol" id="settings-currency-symbol">$</span>
                                <input type="number" id="cost-per-unit" step="0.01" min="0" placeholder="0.15">
                            </div>
                        </div>

                        <div class="setting-item">
                            <label for="low-units-threshold">Low Units Warning Threshold</label>
                            <input type="number" id="low-units-threshold" min="0" placeholder="10">
                            <span class="unit-label" id="settings-unit-label">Units</span>
                        </div>
                    </div>

                    <!-- Appearance Settings -->
                    <div class="settings-section">
                        <h3><span class="section-icon">🎨</span>Appearance</h3>

                        <div class="setting-item">
                            <label for="theme-select">Theme</label>
                            <select id="theme-select">
                                <option value="electric-blue">Electric Blue</option>
                                <option value="sunset-orange">Sunset Orange</option>
                                <option value="forest-green">Forest Green</option>
                                <option value="royal-purple">Royal Purple</option>
                                <option value="midnight-dark">Midnight Dark</option>
                            </select>
                        </div>

                        <div class="setting-item">
                            <label for="font-size-select">Font Size</label>
                            <select id="font-size-select">
                                <option value="small">Small</option>
                                <option value="medium">Medium</option>
                                <option value="large">Large</option>
                            </select>
                        </div>
                    </div>

                    <!-- Notification Settings -->
                    <div class="settings-section">
                        <h3><span class="section-icon">🔔</span>Notifications</h3>

                        <div class="setting-item">
                            <label class="toggle-label">
                                <input type="checkbox" id="notifications-enabled">
                                <span class="toggle-slider"></span>
                                Enable Daily Reminders
                            </label>
                        </div>

                        <div class="setting-item" id="notification-time-container">
                            <label for="notification-time">Reminder Time</label>
                            <input type="time" id="notification-time" value="18:00">
                        </div>

                        <div class="setting-item" id="notification-message-container">
                            <label for="notification-message">Reminder Message</label>
                            <textarea id="notification-message" rows="2" placeholder="Don't forget to record your electricity usage!"></textarea>
                        </div>
                    </div>

                    <!-- Data Management -->
                    <div class="settings-section">
                        <h3><span class="section-icon">💾</span>Data Management</h3>

                        <div class="setting-item">
                            <button class="btn btn-secondary" id="dashboard-reset-btn">
                                <span class="btn-icon">🔄</span>
                                Reset Dashboard Data
                            </button>
                            <small>Clears current units and usage records, keeps purchase history</small>
                        </div>

                        <div class="setting-item">
                            <button class="btn btn-danger" id="factory-reset-btn">
                                <span class="btn-icon">⚠️</span>
                                Factory Reset
                            </button>
                            <small>Clears all data and resets to defaults</small>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="js/app.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/purchases.js"></script>
    <script src="js/usage.js"></script>
    <script src="js/history.js"></script>
    <script src="js/settings.js"></script>
    <script src="js/notifications.js"></script>
    <script src="js/themes.js"></script>
    <script src="js/test-integration.js"></script>
    <script src="test-runner.js"></script>
</body>
</html>
