<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PREPAID USER - ELECTRICITY</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#2196F3">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Prepaid Electricity">
    <link rel="apple-touch-icon" href="assets/icon-192.png">
    <link rel="icon" type="image/png" sizes="192x192" href="assets/icon-192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="assets/icon-512.png">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="logo-container">
                <div class="lightning-logo">⚡</div>
                <h2>PREPAID USER</h2>
                <p>ELECTRICITY</p>
            </div>
            <div class="loading-spinner"></div>
        </div>
    </div>

    <!-- Main App Container -->
    <div id="app" class="app-container" style="display: none;">
        <!-- Header -->
        <header class="app-header">
            <button id="menu-toggle" class="menu-toggle" aria-label="Toggle menu">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <h1 class="app-title">
                <span class="lightning-icon">⚡</span>
                PREPAID USER
            </h1>
            <div class="header-actions">
                <button id="theme-toggle" class="icon-btn" aria-label="Toggle theme">
                    <span class="theme-icon">🌙</span>
                </button>
            </div>
        </header>

        <!-- Sidebar Navigation -->
        <nav id="sidebar" class="sidebar">
            <div class="sidebar-header">
                <div class="logo-container">
                    <div class="lightning-logo">⚡</div>
                    <div class="logo-text">
                        <h3>PREPAID USER</h3>
                        <p>ELECTRICITY</p>
                    </div>
                </div>
            </div>
            <ul class="nav-menu">
                <li><a href="#dashboard" class="nav-link active" data-page="dashboard">
                    <span class="nav-icon">📊</span>Dashboard
                </a></li>
                <li><a href="#purchases" class="nav-link" data-page="purchases">
                    <span class="nav-icon">💰</span>Purchases
                </a></li>
                <li><a href="#usage" class="nav-link" data-page="usage">
                    <span class="nav-icon">📈</span>Usage
                </a></li>
                <li><a href="#history" class="nav-link" data-page="history">
                    <span class="nav-icon">📋</span>History
                </a></li>
                <li><a href="#settings" class="nav-link" data-page="settings">
                    <span class="nav-icon">⚙️</span>Settings
                </a></li>
            </ul>
        </nav>

        <!-- Sidebar Overlay -->
        <div id="sidebar-overlay" class="sidebar-overlay"></div>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Page -->
            <div id="dashboard-page" class="page active">
                <div class="page-header">
                    <h2><span class="page-icon">📊</span>Dashboard</h2>
                </div>
                
                <!-- Quick Actions -->
                <div class="quick-actions">
                    <button class="quick-action-btn" data-action="add-purchase">
                        <span class="action-icon">💰</span>
                        Add Purchase
                    </button>
                    <button class="quick-action-btn" data-action="record-usage">
                        <span class="action-icon">📝</span>
                        Record Usage
                    </button>
                    <button class="quick-action-btn" data-action="view-history">
                        <span class="action-icon">📋</span>
                        View History
                    </button>
                </div>

                <!-- Current Status Cards -->
                <div class="status-cards">
                    <div class="status-card">
                        <div class="card-header">
                            <span class="card-icon">⚡</span>
                            <h3>Current Units</h3>
                        </div>
                        <div class="card-value" id="current-units">0</div>
                        <div class="card-unit" id="current-units-label">Units</div>
                    </div>
                    
                    <div class="status-card">
                        <div class="card-header">
                            <span class="card-icon">📊</span>
                            <h3>Usage Since Last</h3>
                        </div>
                        <div class="card-value" id="usage-since-last">0</div>
                        <div class="card-unit" id="usage-units-label">Units</div>
                    </div>
                </div>

                <!-- Usage Dial -->
                <div class="dial-container">
                    <div class="dial-wrapper">
                        <canvas id="usage-dial" width="300" height="300"></canvas>
                        <div class="dial-center">
                            <div class="dial-value" id="dial-percentage">0%</div>
                            <div class="dial-label">Usage</div>
                        </div>
                    </div>
                </div>

                <!-- Usage Summary -->
                <div class="usage-summary">
                    <div class="summary-card">
                        <h4><span class="summary-icon">📅</span>Weekly Usage</h4>
                        <div class="summary-value">
                            <span id="weekly-units">0</span> <span id="weekly-units-label">Units</span>
                        </div>
                        <div class="summary-cost" id="weekly-cost">$0.00</div>
                    </div>
                    
                    <div class="summary-card">
                        <h4><span class="summary-icon">📆</span>Monthly Usage</h4>
                        <div class="summary-value">
                            <span id="monthly-units">0</span> <span id="monthly-units-label">Units</span>
                        </div>
                        <div class="summary-cost" id="monthly-cost">$0.00</div>
                    </div>
                </div>

                <!-- Low Units Warning -->
                <div id="low-units-warning" class="warning-card" style="display: none;">
                    <div class="warning-icon">⚠️</div>
                    <div class="warning-content">
                        <h4>Low Units Warning</h4>
                        <p>Your electricity units are running low. Consider purchasing more units soon.</p>
                    </div>
                </div>
            </div>

            <!-- Purchases Page -->
            <div id="purchases-page" class="page">
                <div class="page-header">
                    <h2><span class="page-icon">💰</span>Purchases</h2>
                </div>
                <!-- Purchases content will be added -->
            </div>

            <!-- Usage Page -->
            <div id="usage-page" class="page">
                <div class="page-header">
                    <h2><span class="page-icon">📈</span>Usage</h2>
                </div>
                <!-- Usage content will be added -->
            </div>

            <!-- History Page -->
            <div id="history-page" class="page">
                <div class="page-header">
                    <h2><span class="page-icon">📋</span>History</h2>
                </div>
                <!-- History content will be added -->
            </div>

            <!-- Settings Page -->
            <div id="settings-page" class="page">
                <div class="page-header">
                    <h2><span class="page-icon">⚙️</span>Settings</h2>
                </div>
                <!-- Settings content will be added -->
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="js/app.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/purchases.js"></script>
    <script src="js/usage.js"></script>
    <script src="js/history.js"></script>
    <script src="js/settings.js"></script>
    <script src="js/notifications.js"></script>
    <script src="js/themes.js"></script>
</body>
</html>
