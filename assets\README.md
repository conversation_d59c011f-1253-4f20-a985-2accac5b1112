# Assets Directory

This directory contains all the static assets for the PREPAID USER - ELECTRICITY app.

## Required Icons

The following icon sizes are needed for the PWA manifest:

- icon-72.png (72x72)
- icon-96.png (96x96)
- icon-128.png (128x128)
- icon-144.png (144x144)
- icon-152.png (152x152)
- icon-192.png (192x192)
- icon-384.png (384x384)
- icon-512.png (512x512)

## Icon Design Guidelines

The app icon should feature:
- Lightning bolt symbol (⚡) as the main element
- Modern gradient background (electric blue theme)
- Transparent background for flexibility
- Clean, minimalist design
- Good contrast for visibility

## Screenshots

For app store listings, include:
- screenshot-mobile-1.png (390x844) - Dashboard view
- screenshot-mobile-2.png (390x844) - Purchase recording
- screenshot-tablet-1.png (768x1024) - Tablet view

## Creating Icons

You can create the icons using:
1. Design tools like Figma, Adobe Illustrator, or Canva
2. Online icon generators
3. PWA icon generators

The base design should be a lightning bolt (⚡) with a modern gradient background matching the app's electric blue theme.

## Temporary Solution

For development and testing, you can use emoji-based icons or simple colored squares until proper icons are created.
