/**
 * Test Integration System
 * Provides testing functionality within the main app context
 */

class TestIntegration {
    constructor() {
        this.testResults = [];
        this.isTestMode = false;
        this.init();
    }

    init() {
        // Check if we're in test mode (URL parameter or localStorage flag)
        const urlParams = new URLSearchParams(window.location.search);
        const testMode = urlParams.get('test') === 'true' || localStorage.getItem('testMode') === 'true';
        
        if (testMode) {
            this.enableTestMode();
        }

        // Add global test functions
        window.runAppTests = () => this.runAllTests();
        window.testNavigation = () => this.testNavigation();
        window.testPurchases = () => this.testPurchases();
        window.testUsage = () => this.testUsage();
        window.testStorage = () => this.testStorage();
        window.showTestResults = () => this.showTestResults();
        window.runAutomatedTests = () => this.runAutomatedTests();
    }

    enableTestMode() {
        this.isTestMode = true;
        localStorage.setItem('testMode', 'true');
        
        // Add test UI to the app
        this.addTestUI();
        
        console.log('🧪 Test mode enabled');
    }

    disableTestMode() {
        this.isTestMode = false;
        localStorage.removeItem('testMode');
        
        // Remove test UI
        const testUI = document.getElementById('test-ui');
        if (testUI) {
            testUI.remove();
        }
        
        console.log('🧪 Test mode disabled');
    }

    addTestUI() {
        // Create test UI overlay
        const testUI = document.createElement('div');
        testUI.id = 'test-ui';
        testUI.innerHTML = `
            <div style="position: fixed; top: 10px; right: 10px; z-index: 10000; background: rgba(0,0,0,0.9); color: white; padding: 15px; border-radius: 8px; font-family: monospace; font-size: 12px; max-width: 300px;">
                <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 10px;">
                    <strong>🧪 Test Mode</strong>
                    <button onclick="window.testIntegration.disableTestMode()" style="background: #dc3545; color: white; border: none; padding: 2px 6px; border-radius: 3px; margin-left: 10px; cursor: pointer;">×</button>
                </div>
                <div style="margin-bottom: 10px;">
                    <button onclick="window.testIntegration.runAllTests()" style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin: 2px;">Run All Tests</button>
                    <button onclick="window.testIntegration.runAutomatedTests()" style="background: #17a2b8; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin: 2px;">Auto Tests</button>
                </div>
                <div style="margin-bottom: 10px;">
                    <button onclick="window.testIntegration.testNavigation()" style="background: #28a745; color: white; border: none; padding: 3px 8px; border-radius: 3px; cursor: pointer; margin: 1px; font-size: 10px;">Nav</button>
                    <button onclick="window.testIntegration.testPurchases()" style="background: #28a745; color: white; border: none; padding: 3px 8px; border-radius: 3px; cursor: pointer; margin: 1px; font-size: 10px;">Purchase</button>
                    <button onclick="window.testIntegration.testUsage()" style="background: #28a745; color: white; border: none; padding: 3px 8px; border-radius: 3px; cursor: pointer; margin: 1px; font-size: 10px;">Usage</button>
                    <button onclick="window.testIntegration.testStorage()" style="background: #28a745; color: white; border: none; padding: 3px 8px; border-radius: 3px; cursor: pointer; margin: 1px; font-size: 10px;">Storage</button>
                </div>
                <div id="test-status" style="font-size: 10px; opacity: 0.8;">Ready</div>
            </div>
        `;
        
        document.body.appendChild(testUI);
    }

    updateTestStatus(message) {
        const status = document.getElementById('test-status');
        if (status) {
            status.textContent = message;
        }
        console.log(`🧪 ${message}`);
    }

    addTestResult(test, passed, message) {
        this.testResults.push({
            test,
            passed,
            message,
            timestamp: new Date()
        });
    }

    async runAllTests() {
        this.testResults = [];
        this.updateTestStatus('Running all tests...');
        
        await this.testStorage();
        await this.testNavigation();
        await this.testPurchases();
        await this.testUsage();
        
        this.showTestResults();
    }

    async testNavigation() {
        this.updateTestStatus('Testing navigation...');
        
        const pages = ['dashboard', 'purchases', 'usage', 'history', 'settings'];
        let passedTests = 0;
        
        for (const page of pages) {
            const pageElement = document.getElementById(`${page}-page`);
            const navLink = document.querySelector(`[data-page="${page}"]`);
            
            if (pageElement && navLink) {
                this.addTestResult('navigation', true, `${page} page exists and has nav link`);
                passedTests++;
            } else {
                this.addTestResult('navigation', false, `${page} page missing: element=${!!pageElement}, nav=${!!navLink}`);
            }
        }
        
        // Test page switching
        if (window.electricityApp && window.electricityApp.showPage) {
            try {
                window.electricityApp.showPage('purchases');
                const activePage = document.querySelector('.page.active');
                if (activePage && activePage.id === 'purchases-page') {
                    this.addTestResult('navigation', true, 'Page switching works');
                    passedTests++;
                } else {
                    this.addTestResult('navigation', false, 'Page switching failed');
                }
                
                // Switch back to dashboard
                window.electricityApp.showPage('dashboard');
            } catch (error) {
                this.addTestResult('navigation', false, `Page switching error: ${error.message}`);
            }
        } else {
            this.addTestResult('navigation', false, 'Main app or showPage method not available');
        }
        
        this.updateTestStatus(`Navigation: ${passedTests}/${pages.length + 1} tests passed`);
    }

    async testPurchases() {
        this.updateTestStatus('Testing purchases...');
        
        // Check if purchases manager exists
        if (typeof window.purchasesManager === 'undefined') {
            this.addTestResult('purchases', false, 'PurchasesManager not loaded');
            return;
        }
        
        this.addTestResult('purchases', true, 'PurchasesManager loaded');
        
        // Check form elements
        const form = document.getElementById('purchase-form');
        const amountInput = document.getElementById('purchase-amount');
        const unitsInput = document.getElementById('purchase-units');
        
        if (form && amountInput && unitsInput) {
            this.addTestResult('purchases', true, 'Purchase form elements found');
            
            // Test form interaction
            try {
                const originalAmount = amountInput.value;
                const originalUnits = unitsInput.value;
                
                amountInput.value = '25.00';
                unitsInput.value = '100';
                
                // Trigger calculation
                amountInput.dispatchEvent(new Event('input'));
                
                this.addTestResult('purchases', true, 'Form interaction test passed');
                
                // Restore original values
                amountInput.value = originalAmount;
                unitsInput.value = originalUnits;
            } catch (error) {
                this.addTestResult('purchases', false, `Form interaction failed: ${error.message}`);
            }
        } else {
            this.addTestResult('purchases', false, 'Purchase form elements missing');
        }
        
        this.updateTestStatus('Purchases tests completed');
    }

    async testUsage() {
        this.updateTestStatus('Testing usage...');
        
        // Check if usage manager exists
        if (typeof window.usageManager === 'undefined') {
            this.addTestResult('usage', false, 'UsageManager not loaded');
            return;
        }
        
        this.addTestResult('usage', true, 'UsageManager loaded');
        
        // Check form elements
        const form = document.getElementById('usage-form');
        const newUnitsInput = document.getElementById('new-units');
        const currentUnitsDisplay = document.getElementById('current-units-display');
        
        if (form && newUnitsInput && currentUnitsDisplay) {
            this.addTestResult('usage', true, 'Usage form elements found');
        } else {
            this.addTestResult('usage', false, 'Usage form elements missing');
        }
        
        // Check chart
        const chart = document.getElementById('usage-chart');
        if (chart) {
            this.addTestResult('usage', true, 'Usage chart element found');
        } else {
            this.addTestResult('usage', false, 'Usage chart element missing');
        }
        
        this.updateTestStatus('Usage tests completed');
    }

    async testStorage() {
        this.updateTestStatus('Testing storage...');
        
        try {
            // Test storage manager
            if (typeof StorageManager === 'undefined') {
                this.addTestResult('storage', false, 'StorageManager not available');
                return;
            }
            
            this.addTestResult('storage', true, 'StorageManager available');
            
            // Test basic operations
            const originalUnits = StorageManager.getCurrentUnits();
            StorageManager.setCurrentUnits(123.45);
            const testUnits = StorageManager.getCurrentUnits();
            
            if (testUnits === 123.45) {
                this.addTestResult('storage', true, 'Units storage/retrieval works');
            } else {
                this.addTestResult('storage', false, `Units storage failed: expected 123.45, got ${testUnits}`);
            }
            
            // Restore original value
            StorageManager.setCurrentUnits(originalUnits);
            
            // Test settings
            const settings = StorageManager.getSettings();
            if (settings && typeof settings === 'object') {
                this.addTestResult('storage', true, 'Settings retrieval works');
            } else {
                this.addTestResult('storage', false, 'Settings retrieval failed');
            }
            
        } catch (error) {
            this.addTestResult('storage', false, `Storage test error: ${error.message}`);
        }
        
        this.updateTestStatus('Storage tests completed');
    }

    showTestResults() {
        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        
        console.log(`🧪 Test Results: ${passed}/${total} passed`);
        console.table(this.testResults);
        
        this.updateTestStatus(`${passed}/${total} tests passed`);
        
        // Show results in alert for now (could be improved with a modal)
        const summary = this.testResults.map(r => 
            `${r.passed ? '✅' : '❌'} ${r.test}: ${r.message}`
        ).join('\n');
        
        alert(`Test Results (${passed}/${total} passed):\n\n${summary}`);
    }

    async runAutomatedTests() {
        this.updateTestStatus('Running automated tests...');

        if (typeof AutomatedTestRunner !== 'undefined') {
            const runner = new AutomatedTestRunner();
            const results = await runner.runAllTests();

            this.updateTestStatus(`Automated: ${results.passedTests}/${results.totalTests} passed (${results.successRate.toFixed(1)}%)`);

            // Show summary
            alert(`Automated Test Results:\n\n✅ Passed: ${results.passedTests}\n❌ Failed: ${results.failedTests}\n📊 Success Rate: ${results.successRate.toFixed(1)}%\n\nCheck console for detailed results.`);
        } else {
            this.updateTestStatus('AutomatedTestRunner not available');
            alert('Automated test runner not available. Please check if test-runner.js is loaded.');
        }
    }
}

// Initialize test integration when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.testIntegration = new TestIntegration();
});
