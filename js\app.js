// Main App Controller
class ElectricityApp {
    constructor() {
        this.currentPage = 'dashboard';
        this.isInitialized = false;
        this.init();
    }

    async init() {
        try {
            // Show loading screen
            this.showLoading();
            
            // Initialize storage
            await StorageManager.init();
            
            // Check if app needs initial setup
            if (!StorageManager.isInitialized()) {
                await this.showInitialSetup();
            }
            
            // Initialize all modules
            await this.initializeModules();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Hide loading screen and show app
            this.hideLoading();
            
            // Load initial page
            this.showPage('dashboard');
            
            this.isInitialized = true;
            console.log('Electricity App initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize app:', error);
            this.showError('Failed to initialize app. Please refresh the page.');
        }
    }

    async initializeModules() {
        // Initialize all app modules
        if (typeof DashboardManager !== 'undefined') {
            window.dashboardManager = new DashboardManager();
        }
        
        if (typeof PurchasesManager !== 'undefined') {
            window.purchasesManager = new PurchasesManager();
        }
        
        if (typeof UsageManager !== 'undefined') {
            window.usageManager = new UsageManager();
        }
        
        if (typeof HistoryManager !== 'undefined') {
            window.historyManager = new HistoryManager();
        }
        
        if (typeof SettingsManager !== 'undefined') {
            window.settingsManager = new SettingsManager();
        }
        
        if (typeof NotificationManager !== 'undefined') {
            window.notificationManager = new NotificationManager();
        }
        
        if (typeof ThemeManager !== 'undefined') {
            window.themeManager = new ThemeManager();
            await window.themeManager.init();
        }
    }

    setupEventListeners() {
        // Menu toggle
        const menuToggle = document.getElementById('menu-toggle');
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebar-overlay');
        
        menuToggle?.addEventListener('click', () => this.toggleSidebar());
        sidebarOverlay?.addEventListener('click', () => this.closeSidebar());
        
        // Navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.dataset.page;
                if (page) {
                    this.showPage(page);
                    this.closeSidebar();
                }
            });
        });
        
        // Quick action buttons
        document.querySelectorAll('.quick-action-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = btn.dataset.action;
                this.handleQuickAction(action);
            });
        });
        
        // Theme toggle
        const themeToggle = document.getElementById('theme-toggle');
        themeToggle?.addEventListener('click', () => {
            if (window.themeManager) {
                window.themeManager.toggleTheme();
            }
        });
        
        // Handle back button
        window.addEventListener('popstate', (e) => {
            const page = e.state?.page || 'dashboard';
            this.showPage(page, false);
        });
        
        // Handle app visibility change
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.isInitialized) {
                this.refreshCurrentPage();
            }
        });
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        
        sidebar?.classList.toggle('open');
        overlay?.classList.toggle('active');
    }

    closeSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        
        sidebar?.classList.remove('open');
        overlay?.classList.remove('active');
    }

    showPage(pageName, updateHistory = true) {
        // Hide all pages
        document.querySelectorAll('.page').forEach(page => {
            page.classList.remove('active');
        });
        
        // Show target page
        const targetPage = document.getElementById(`${pageName}-page`);
        if (targetPage) {
            targetPage.classList.add('active');
            this.currentPage = pageName;
            
            // Update navigation
            this.updateNavigation(pageName);
            
            // Update browser history
            if (updateHistory) {
                history.pushState({ page: pageName }, '', `#${pageName}`);
            }
            
            // Refresh page content
            this.refreshPage(pageName);
        }
    }

    updateNavigation(activePage) {
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
            if (link.dataset.page === activePage) {
                link.classList.add('active');
            }
        });
    }

    refreshPage(pageName) {
        switch (pageName) {
            case 'dashboard':
                if (window.dashboardManager) {
                    window.dashboardManager.refresh();
                }
                break;
            case 'purchases':
                if (window.purchasesManager) {
                    window.purchasesManager.refresh();
                }
                break;
            case 'usage':
                if (window.usageManager) {
                    window.usageManager.refresh();
                }
                break;
            case 'history':
                if (window.historyManager) {
                    window.historyManager.refresh();
                }
                break;
            case 'settings':
                if (window.settingsManager) {
                    window.settingsManager.refresh();
                }
                break;
        }
    }

    refreshCurrentPage() {
        this.refreshPage(this.currentPage);
    }

    handleQuickAction(action) {
        switch (action) {
            case 'add-purchase':
                this.showPage('purchases');
                break;
            case 'record-usage':
                this.showPage('usage');
                break;
            case 'view-history':
                this.showPage('history');
                break;
        }
    }

    showLoading() {
        const loadingScreen = document.getElementById('loading-screen');
        const app = document.getElementById('app');
        
        if (loadingScreen) loadingScreen.style.display = 'flex';
        if (app) app.style.display = 'none';
    }

    hideLoading() {
        const loadingScreen = document.getElementById('loading-screen');
        const app = document.getElementById('app');
        
        setTimeout(() => {
            if (loadingScreen) {
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                    if (app) app.style.display = 'flex';
                }, 300);
            }
        }, 1000); // Show loading for at least 1 second
    }

    async showInitialSetup() {
        // This will be implemented when we create the initial setup modal
        console.log('Initial setup required');
    }

    showError(message) {
        // Simple error display - can be enhanced later
        alert(message);
    }

    // Utility methods
    formatCurrency(amount, currency = null) {
        const settings = StorageManager.getSettings();
        const currencySymbol = currency || settings.currency.symbol;
        return `${currencySymbol}${amount.toFixed(2)}`;
    }

    formatUnits(amount, unit = null) {
        const settings = StorageManager.getSettings();
        const unitName = unit || settings.unit.name;
        return `${amount} ${unitName}`;
    }

    showNotification(message, type = 'info') {
        // Simple notification - can be enhanced later
        console.log(`${type.toUpperCase()}: ${message}`);
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.electricityApp = new ElectricityApp();
});

// Service Worker registration for PWA functionality
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}
