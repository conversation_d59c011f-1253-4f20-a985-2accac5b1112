// Theme Manager - Handles theme switching and customization
class ThemeManager {
    constructor() {
        this.themes = {
            'electric-blue': {
                name: 'Electric Blue',
                colors: {
                    primary: '#2196F3',
                    primaryDark: '#1976D2',
                    primaryLight: '#BBDEFB',
                    secondary: '#FF9800',
                    accent: '#4CAF50',
                    background: '#F5F5F5',
                    surface: '#FFFFFF',
                    textPrimary: '#212121',
                    textSecondary: '#757575',
                    textHint: '#BDBDBD'
                }
            },
            'emerald-green': {
                name: 'Emerald Green',
                colors: {
                    primary: '#4CAF50',
                    primaryDark: '#388E3C',
                    primaryLight: '#C8E6C9',
                    secondary: '#FF9800',
                    accent: '#2196F3',
                    background: '#F1F8E9',
                    surface: '#FFFFFF',
                    textPrimary: '#1B5E20',
                    textSecondary: '#4CAF50',
                    textHint: '#81C784'
                }
            },
            'sunset-orange': {
                name: 'Sunset Orange',
                colors: {
                    primary: '#FF9800',
                    primaryDark: '#F57C00',
                    primaryLight: '#FFE0B2',
                    secondary: '#E91E63',
                    accent: '#9C27B0',
                    background: '#FFF8E1',
                    surface: '#FFFFFF',
                    textPrimary: '#E65100',
                    textSecondary: '#FF9800',
                    textHint: '#FFCC02'
                }
            },
            'royal-purple': {
                name: 'Royal Purple',
                colors: {
                    primary: '#9C27B0',
                    primaryDark: '#7B1FA2',
                    primaryLight: '#E1BEE7',
                    secondary: '#FF5722',
                    accent: '#3F51B5',
                    background: '#F3E5F5',
                    surface: '#FFFFFF',
                    textPrimary: '#4A148C',
                    textSecondary: '#9C27B0',
                    textHint: '#BA68C8'
                }
            },
            'midnight-dark': {
                name: 'Midnight Dark',
                colors: {
                    primary: '#BB86FC',
                    primaryDark: '#985EFF',
                    primaryLight: '#D7C7FF',
                    secondary: '#03DAC6',
                    accent: '#CF6679',
                    background: '#121212',
                    surface: '#1E1E1E',
                    textPrimary: '#FFFFFF',
                    textSecondary: '#B3B3B3',
                    textHint: '#666666'
                }
            }
        };
        
        this.currentTheme = 'electric-blue';
    }

    async init() {
        // Load saved theme
        const settings = StorageManager.getSettings();
        this.currentTheme = settings.theme || 'electric-blue';
        
        // Apply theme
        this.applyTheme(this.currentTheme);
        
        // Setup theme toggle button
        this.setupThemeToggle();
    }

    applyTheme(themeName) {
        const theme = this.themes[themeName];
        if (!theme) {
            console.warn(`Theme ${themeName} not found, using default`);
            themeName = 'electric-blue';
        }

        this.currentTheme = themeName;
        const colors = this.themes[themeName].colors;
        
        // Apply CSS custom properties
        const root = document.documentElement;
        
        root.style.setProperty('--primary-color', colors.primary);
        root.style.setProperty('--primary-dark', colors.primaryDark);
        root.style.setProperty('--primary-light', colors.primaryLight);
        root.style.setProperty('--secondary-color', colors.secondary);
        root.style.setProperty('--accent-color', colors.accent);
        root.style.setProperty('--background-color', colors.background);
        root.style.setProperty('--surface-color', colors.surface);
        root.style.setProperty('--text-primary', colors.textPrimary);
        root.style.setProperty('--text-secondary', colors.textSecondary);
        root.style.setProperty('--text-hint', colors.textHint);
        
        // Update gradients
        root.style.setProperty('--gradient-primary', `linear-gradient(135deg, ${colors.primary}, ${colors.primaryDark})`);
        root.style.setProperty('--gradient-secondary', `linear-gradient(135deg, ${colors.secondary}, ${this.darkenColor(colors.secondary, 20)})`);
        
        // Update body class for theme-specific styles
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        document.body.classList.add(`theme-${themeName}`);
        
        // Update theme toggle icon
        this.updateThemeToggleIcon(themeName);
        
        // Save theme preference
        StorageManager.updateSetting('theme', themeName);
        
        console.log(`Applied theme: ${theme.name}`);
    }

    setupThemeToggle() {
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }
    }

    toggleTheme() {
        const themeOrder = ['electric-blue', 'emerald-green', 'sunset-orange', 'royal-purple', 'midnight-dark'];
        const currentIndex = themeOrder.indexOf(this.currentTheme);
        const nextIndex = (currentIndex + 1) % themeOrder.length;
        const nextTheme = themeOrder[nextIndex];
        
        this.applyTheme(nextTheme);
        
        // Show theme change notification
        if (window.electricityApp) {
            window.electricityApp.showNotification(`Theme changed to ${this.themes[nextTheme].name}`, 'info');
        }
    }

    updateThemeToggleIcon(themeName) {
        const themeToggle = document.getElementById('theme-toggle');
        const themeIcon = themeToggle?.querySelector('.theme-icon');
        
        if (themeIcon) {
            const iconMap = {
                'electric-blue': '💙',
                'emerald-green': '💚',
                'sunset-orange': '🧡',
                'royal-purple': '💜',
                'midnight-dark': '🖤'
            };
            
            themeIcon.textContent = iconMap[themeName] || '🌙';
        }
    }

    darkenColor(color, percent) {
        // Simple color darkening function
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) - amt;
        const G = (num >> 8 & 0x00FF) - amt;
        const B = (num & 0x0000FF) - amt;
        
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }

    getCurrentTheme() {
        return this.currentTheme;
    }

    getThemeList() {
        return Object.keys(this.themes).map(key => ({
            key,
            name: this.themes[key].name
        }));
    }

    // Method to add custom themes in the future
    addCustomTheme(key, theme) {
        this.themes[key] = theme;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeManager;
}
