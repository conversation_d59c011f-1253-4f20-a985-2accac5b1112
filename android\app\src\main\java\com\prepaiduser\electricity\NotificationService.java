package com.prepaiduser.electricity;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import androidx.annotation.Nullable;

public class NotificationService extends Service {
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        // Handle background notification scheduling
        return START_STICKY;
    }
    
    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
