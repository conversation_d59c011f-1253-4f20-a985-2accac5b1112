// Usage Manager - Handles usage recording and chart visualization
class UsageManager {
    constructor() {
        this.chart = null;
        this.chartCanvas = null;
        this.chartContext = null;
        this.init();
    }

    init() {
        this.createUsagePage();
        this.setupEventListeners();
        this.setupChart();
        this.refresh();
    }

    createUsagePage() {
        const usagePage = document.getElementById('usage-page');
        if (!usagePage) return;

        usagePage.innerHTML = `
            <div class="page-header">
                <h2><span class="page-icon">📈</span>Usage</h2>
                <p class="page-description">Record your current electricity units and track usage patterns</p>
            </div>

            <!-- Usage Recording Form -->
            <div class="usage-form-container">
                <div class="form-card">
                    <h3><span class="form-icon">📝</span>Record Current Units</h3>
                    
                    <div class="current-status">
                        <div class="status-item">
                            <span class="status-label">Previous Reading:</span>
                            <span class="status-value" id="previous-reading">0.0 Units</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">Current Total:</span>
                            <span class="status-value" id="current-total">0.0 Units</span>
                        </div>
                    </div>

                    <div class="input-group">
                        <label for="current-units-input">
                            <span class="input-icon">⚡</span>
                            Current Units Reading
                        </label>
                        <div class="input-wrapper">
                            <input type="number" id="current-units-input" placeholder="0.0" step="0.1" min="0">
                            <span class="units-symbol" id="usage-units-symbol">Units</span>
                        </div>
                        <small class="input-help">Enter the current reading from your electricity meter</small>
                    </div>

                    <!-- Usage Calculation Preview -->
                    <div class="usage-preview" id="usage-preview">
                        <h4><span class="preview-icon">🔢</span>Usage Calculation</h4>
                        <div class="calculation-display">
                            <div class="calculation-item">
                                <span class="calc-label">Previous:</span>
                                <span class="calc-value" id="calc-previous">0.0</span>
                            </div>
                            <div class="calculation-operator">-</div>
                            <div class="calculation-item">
                                <span class="calc-label">Current:</span>
                                <span class="calc-value" id="calc-current">0.0</span>
                            </div>
                            <div class="calculation-operator">=</div>
                            <div class="calculation-item result">
                                <span class="calc-label">Usage:</span>
                                <span class="calc-value" id="calc-usage">0.0</span>
                            </div>
                        </div>
                        <div class="usage-cost">
                            <span class="cost-label">Estimated Cost:</span>
                            <span class="cost-value" id="usage-cost-value">$0.00</span>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" id="clear-usage-btn" class="btn-secondary">
                            <span class="btn-icon">🗑️</span>Clear
                        </button>
                        <button type="button" id="save-usage-btn" class="btn-primary">
                            <span class="btn-icon">💾</span>Record Usage
                        </button>
                    </div>
                </div>
            </div>

            <!-- Usage Chart -->
            <div class="chart-container">
                <h3><span class="section-icon">📊</span>Usage Chart</h3>
                <div class="chart-wrapper">
                    <canvas id="usage-chart" width="400" height="300"></canvas>
                </div>
                <div class="chart-controls">
                    <button class="chart-period-btn active" data-period="week">Week</button>
                    <button class="chart-period-btn" data-period="month">Month</button>
                    <button class="chart-period-btn" data-period="all">All Time</button>
                </div>
            </div>

            <!-- Usage Statistics -->
            <div class="usage-stats">
                <h3><span class="section-icon">📋</span>Usage Statistics</h3>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">📅</div>
                        <div class="stat-content">
                            <div class="stat-label">This Week</div>
                            <div class="stat-value" id="week-usage">0.0 Units</div>
                            <div class="stat-cost" id="week-cost">$0.00</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">📆</div>
                        <div class="stat-content">
                            <div class="stat-label">This Month</div>
                            <div class="stat-value" id="month-usage">0.0 Units</div>
                            <div class="stat-cost" id="month-cost">$0.00</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">📈</div>
                        <div class="stat-content">
                            <div class="stat-label">Daily Average</div>
                            <div class="stat-value" id="daily-average">0.0 Units</div>
                            <div class="stat-cost" id="daily-cost">$0.00</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">⚡</div>
                        <div class="stat-content">
                            <div class="stat-label">Total Records</div>
                            <div class="stat-value" id="total-records">0</div>
                            <div class="stat-cost">Entries</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Usage Records -->
            <div class="recent-usage">
                <h3><span class="section-icon">📋</span>Recent Records</h3>
                <div id="recent-usage-list" class="usage-list">
                    <!-- Recent usage records will be populated here -->
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        const currentUnitsInput = document.getElementById('current-units-input');
        const saveUsageBtn = document.getElementById('save-usage-btn');
        const clearUsageBtn = document.getElementById('clear-usage-btn');

        // Live calculation on input
        currentUnitsInput?.addEventListener('input', () => this.updateUsageCalculation());

        // Form actions
        saveUsageBtn?.addEventListener('click', () => this.saveUsage());
        clearUsageBtn?.addEventListener('click', () => this.clearForm());

        // Enter key to save
        currentUnitsInput?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.saveUsage();
        });

        // Chart period controls
        document.querySelectorAll('.chart-period-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.chart-period-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.updateChart(btn.dataset.period);
            });
        });
    }

    setupChart() {
        this.chartCanvas = document.getElementById('usage-chart');
        if (!this.chartCanvas) return;
        
        this.chartContext = this.chartCanvas.getContext('2d');
        
        // Set canvas size for high DPI displays
        const rect = this.chartCanvas.getBoundingClientRect();
        const dpr = window.devicePixelRatio || 1;
        
        this.chartCanvas.width = rect.width * dpr;
        this.chartCanvas.height = rect.height * dpr;
        this.chartContext.scale(dpr, dpr);
        
        this.updateChart('week');
    }

    refresh() {
        this.updateCurrentStatus();
        this.updateUnitsSymbols();
        this.updateUsageCalculation();
        this.updateUsageStats();
        this.loadRecentUsage();
        this.updateChart('week');
    }

    updateCurrentStatus() {
        const currentUnits = StorageManager.getCurrentUnits();
        const lastRecord = StorageManager.getLastUsageRecord();
        const settings = StorageManager.getSettings();
        
        const previousReadingElement = document.getElementById('previous-reading');
        const currentTotalElement = document.getElementById('current-total');
        
        if (previousReadingElement) {
            const previousReading = lastRecord ? lastRecord.currentUnits : currentUnits;
            previousReadingElement.textContent = `${previousReading.toFixed(1)} ${settings.unit.name}`;
        }
        
        if (currentTotalElement) {
            currentTotalElement.textContent = `${currentUnits.toFixed(1)} ${settings.unit.name}`;
        }
    }

    updateUnitsSymbols() {
        const settings = StorageManager.getSettings();
        const unitName = settings.unit.name;
        
        const unitsSymbolElement = document.getElementById('usage-units-symbol');
        if (unitsSymbolElement) {
            unitsSymbolElement.textContent = unitName;
        }
    }

    updateUsageCalculation() {
        const currentUnitsInput = document.getElementById('current-units-input');
        const inputValue = parseFloat(currentUnitsInput?.value || 0);
        
        const currentUnits = StorageManager.getCurrentUnits();
        const settings = StorageManager.getSettings();
        
        const usage = Math.max(0, currentUnits - inputValue);
        const cost = usage * settings.costPerUnit;
        
        // Update calculation display
        const calcPreviousElement = document.getElementById('calc-previous');
        const calcCurrentElement = document.getElementById('calc-current');
        const calcUsageElement = document.getElementById('calc-usage');
        const usageCostElement = document.getElementById('usage-cost-value');
        
        if (calcPreviousElement) calcPreviousElement.textContent = currentUnits.toFixed(1);
        if (calcCurrentElement) calcCurrentElement.textContent = inputValue.toFixed(1);
        if (calcUsageElement) calcUsageElement.textContent = usage.toFixed(1);
        if (usageCostElement) usageCostElement.textContent = `${settings.currency.symbol}${cost.toFixed(2)}`;
    }

    saveUsage() {
        const currentUnitsInput = document.getElementById('current-units-input');
        const inputValue = parseFloat(currentUnitsInput?.value || 0);
        
        if (inputValue < 0) {
            this.showMessage('Please enter a valid units reading.', 'error');
            return;
        }
        
        const currentUnits = StorageManager.getCurrentUnits();
        
        if (inputValue > currentUnits) {
            this.showMessage('Current reading cannot be higher than your total units.', 'error');
            return;
        }
        
        const usage = currentUnits - inputValue;
        
        if (usage <= 0) {
            this.showMessage('No usage detected. Please check your reading.', 'warning');
            return;
        }
        
        // Save usage record
        const record = StorageManager.addUsageRecord({
            previousUnits: currentUnits,
            currentUnits: inputValue,
            usage: usage
        });
        
        if (record) {
            // Update current units
            StorageManager.setCurrentUnits(inputValue);
            
            // Clear form and refresh
            this.clearForm();
            this.refresh();
            
            // Refresh dashboard if available
            if (window.dashboardManager) {
                window.dashboardManager.refresh();
            }
            
            const settings = StorageManager.getSettings();
            this.showMessage(`Usage recorded! ${usage.toFixed(1)} ${settings.unit.name} used.`, 'success');
        } else {
            this.showMessage('Failed to save usage record. Please try again.', 'error');
        }
    }

    clearForm() {
        const currentUnitsInput = document.getElementById('current-units-input');
        if (currentUnitsInput) currentUnitsInput.value = '';
        this.updateUsageCalculation();
    }

    updateUsageStats() {
        const analytics = StorageManager.getUsageAnalytics();
        const settings = StorageManager.getSettings();
        
        // Weekly stats
        const weekUsageElement = document.getElementById('week-usage');
        const weekCostElement = document.getElementById('week-cost');
        
        if (weekUsageElement) {
            weekUsageElement.textContent = `${analytics.weekly.usage.toFixed(1)} ${settings.unit.name}`;
        }
        if (weekCostElement) {
            weekCostElement.textContent = `${settings.currency.symbol}${analytics.weekly.cost.toFixed(2)}`;
        }
        
        // Monthly stats
        const monthUsageElement = document.getElementById('month-usage');
        const monthCostElement = document.getElementById('month-cost');
        
        if (monthUsageElement) {
            monthUsageElement.textContent = `${analytics.monthly.usage.toFixed(1)} ${settings.unit.name}`;
        }
        if (monthCostElement) {
            monthCostElement.textContent = `${settings.currency.symbol}${analytics.monthly.cost.toFixed(2)}`;
        }
        
        // Daily average
        const dailyAverageElement = document.getElementById('daily-average');
        const dailyCostElement = document.getElementById('daily-cost');
        const dailyAverage = analytics.monthly.records > 0 ? analytics.monthly.usage / 30 : 0;
        const dailyCost = dailyAverage * settings.costPerUnit;
        
        if (dailyAverageElement) {
            dailyAverageElement.textContent = `${dailyAverage.toFixed(1)} ${settings.unit.name}`;
        }
        if (dailyCostElement) {
            dailyCostElement.textContent = `${settings.currency.symbol}${dailyCost.toFixed(2)}`;
        }
        
        // Total records
        const totalRecordsElement = document.getElementById('total-records');
        if (totalRecordsElement) {
            totalRecordsElement.textContent = analytics.total.records.toString();
        }
    }

    loadRecentUsage() {
        const records = StorageManager.getUsageRecords().slice(0, 5); // Last 5 records
        const usageList = document.getElementById('recent-usage-list');
        
        if (!usageList) return;
        
        if (records.length === 0) {
            usageList.innerHTML = `
                <div class="empty-state">
                    <span class="empty-icon">📊</span>
                    <p>No usage records yet</p>
                    <small>Record your first usage above</small>
                </div>
            `;
            return;
        }
        
        const settings = StorageManager.getSettings();
        
        usageList.innerHTML = records.map(record => `
            <div class="usage-item">
                <div class="usage-info">
                    <div class="usage-amount">
                        ${record.usage.toFixed(1)} ${settings.unit.name} used
                    </div>
                    <div class="usage-details">
                        <span class="usage-date">${new Date(record.timestamp).toLocaleDateString()}</span>
                        <span class="usage-time">${new Date(record.timestamp).toLocaleTimeString()}</span>
                        <span class="usage-reading">${record.previousUnits.toFixed(1)} → ${record.currentUnits.toFixed(1)}</span>
                    </div>
                </div>
                <div class="usage-cost">
                    ${settings.currency.symbol}${(record.usage * settings.costPerUnit).toFixed(2)}
                </div>
            </div>
        `).join('');
    }

    updateChart(period = 'week') {
        if (!this.chartContext) return;
        
        const records = StorageManager.getUsageRecords();
        const now = new Date();
        let filteredRecords = [];
        
        switch (period) {
            case 'week':
                const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                filteredRecords = records.filter(r => new Date(r.timestamp) >= weekAgo);
                break;
            case 'month':
                const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                filteredRecords = records.filter(r => new Date(r.timestamp) >= monthAgo);
                break;
            case 'all':
                filteredRecords = records;
                break;
        }
        
        this.drawChart(filteredRecords.reverse()); // Reverse to show chronological order
    }

    drawChart(records) {
        const ctx = this.chartContext;
        const canvas = this.chartCanvas;
        const width = canvas.width / (window.devicePixelRatio || 1);
        const height = canvas.height / (window.devicePixelRatio || 1);
        
        // Clear canvas
        ctx.clearRect(0, 0, width, height);
        
        if (records.length === 0) {
            // Draw empty state
            ctx.fillStyle = '#BDBDBD';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('No usage data available', width / 2, height / 2);
            return;
        }
        
        // Chart dimensions
        const padding = 40;
        const chartWidth = width - 2 * padding;
        const chartHeight = height - 2 * padding;
        
        // Find max usage for scaling
        const maxUsage = Math.max(...records.map(r => r.usage));
        const scale = chartHeight / (maxUsage * 1.1); // 10% padding at top
        
        // Draw axes
        ctx.strokeStyle = '#E0E0E0';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(padding, padding);
        ctx.lineTo(padding, height - padding);
        ctx.lineTo(width - padding, height - padding);
        ctx.stroke();
        
        // Draw bars
        const barWidth = chartWidth / records.length * 0.8;
        const barSpacing = chartWidth / records.length * 0.2;
        
        records.forEach((record, index) => {
            const barHeight = record.usage * scale;
            const x = padding + index * (barWidth + barSpacing) + barSpacing / 2;
            const y = height - padding - barHeight;
            
            // Create colorful gradient palette
            const colorPalette = [
                ['#FF6B6B', '#FF8E8E'], // Red gradient
                ['#4ECDC4', '#45B7B8'], // Teal gradient
                ['#45B7D1', '#6C5CE7'], // Blue to purple
                ['#FFA726', '#FFB74D'], // Orange gradient
                ['#66BB6A', '#81C784'], // Green gradient
                ['#AB47BC', '#BA68C8'], // Purple gradient
                ['#26A69A', '#4DB6AC'], // Cyan gradient
                ['#FF7043', '#FF8A65']  // Deep orange gradient
            ];

            // Select color from palette (cycle through colors)
            const colorIndex = index % colorPalette.length;
            const [startColor, endColor] = colorPalette[colorIndex];

            // Create colorful gradient for each bar
            const gradient = ctx.createLinearGradient(x, y + barHeight, x, y);
            gradient.addColorStop(0, startColor);
            gradient.addColorStop(1, endColor);

            // Draw bar with gradient and shadow
            ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
            ctx.shadowBlur = 8;
            ctx.shadowOffsetY = 4;

            ctx.fillStyle = gradient;
            ctx.fillRect(x, y, barWidth, barHeight);

            // Reset shadow
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.shadowOffsetY = 0;

            // Draw highlight on top of bar
            const highlightGradient = ctx.createLinearGradient(x, y, x, y + 20);
            highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.4)');
            highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

            ctx.fillStyle = highlightGradient;
            ctx.fillRect(x, y, barWidth, Math.min(20, barHeight));

            // Draw value on top of bar with better styling
            ctx.fillStyle = '#2C3E50';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeText(record.usage.toFixed(1), x + barWidth / 2, y - 8);
            ctx.fillText(record.usage.toFixed(1), x + barWidth / 2, y - 8);
        });
        
        // Draw labels
        ctx.fillStyle = '#757575';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        
        records.forEach((record, index) => {
            const x = padding + index * (barWidth + barSpacing) + barSpacing / 2 + barWidth / 2;
            const date = new Date(record.timestamp);
            const label = `${date.getMonth() + 1}/${date.getDate()}`;
            ctx.fillText(label, x, height - padding + 15);
        });
    }

    showMessage(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
        if (window.electricityApp) {
            window.electricityApp.showNotification(message, type);
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UsageManager;
}
