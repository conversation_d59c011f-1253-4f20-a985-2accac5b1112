// Purchases Manager - Handles purchase recording and calculations
class PurchasesManager {
    constructor() {
        this.currentCurrency = 0;
        this.currentUnits = 0;
        this.isCalculating = false;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.refresh();
    }

    setupEventListeners() {
        const form = document.getElementById('purchase-form');
        const currencyInput = document.getElementById('purchase-amount');
        const unitsInput = document.getElementById('purchase-units');

        // Form submission
        form?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.savePurchase();
        });

        // Live calculation on input
        currencyInput?.addEventListener('input', () => this.updateLiveCalculation());
        unitsInput?.addEventListener('input', () => this.updateLiveCalculation());

        // Enter key navigation
        currencyInput?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                unitsInput?.focus();
            }
        });

        unitsInput?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.savePurchase();
            }
        });
    }

    refresh() {
        this.updateCurrencySymbols();
        this.updateUnitsSymbols();
        this.updateLiveCalculation();
        this.loadRecentPurchases();
    }

    updateCurrencySymbols() {
        const settings = StorageManager.getSettings();
        const currencySymbol = settings.currency.symbol;

        const currencySymbolElement = document.getElementById('purchase-currency-symbol');
        if (currencySymbolElement) {
            currencySymbolElement.textContent = currencySymbol;
        }
    }

    updateUnitsSymbols() {
        const settings = StorageManager.getSettings();
        const unitName = settings.unit.name;

        const unitsSymbolElement = document.getElementById('purchase-unit-label');
        if (unitsSymbolElement) {
            unitsSymbolElement.textContent = unitName;
        }
    }

    updateLiveCalculation() {
        if (this.isCalculating) return;
        this.isCalculating = true;

        const currencyInput = document.getElementById('purchase-amount');
        const unitsInput = document.getElementById('purchase-units');

        const currencyValue = parseFloat(currencyInput?.value || 0);
        const unitsValue = parseFloat(unitsInput?.value || 0);

        const settings = StorageManager.getSettings();
        const currentUnits = StorageManager.getCurrentUnits();

        // Calculate cost per unit
        let costPerUnit = 0;
        if (unitsValue > 0 && currencyValue > 0) {
            costPerUnit = currencyValue / unitsValue;
        } else if (currencyValue > 0) {
            costPerUnit = settings.costPerUnit;
        }

        // Calculate units if only currency is entered
        let calculatedUnits = unitsValue;
        if (currencyValue > 0 && unitsValue === 0) {
            calculatedUnits = currencyValue / settings.costPerUnit;
            if (unitsInput) {
                unitsInput.value = calculatedUnits.toFixed(1);
            }
        }

        // Calculate currency if only units are entered
        let calculatedCurrency = currencyValue;
        if (unitsValue > 0 && currencyValue === 0) {
            calculatedCurrency = unitsValue * settings.costPerUnit;
            if (currencyInput) {
                currencyInput.value = calculatedCurrency.toFixed(2);
            }
        }

        const newTotal = currentUnits + (calculatedUnits || unitsValue);

        // Update preview
        this.updatePreviewDisplay({
            costPerUnit: costPerUnit,
            totalCost: calculatedCurrency || currencyValue,
            currentUnits: currentUnits,
            newTotal: newTotal,
            settings: settings
        });

        this.isCalculating = false;
    }

    updatePreviewDisplay(data) {
        const costPerUnitElement = document.getElementById('preview-cost-per-unit');
        const currentUnitsElement = document.getElementById('preview-current-units');
        const newTotalElement = document.getElementById('preview-new-total');

        if (costPerUnitElement) {
            costPerUnitElement.textContent = `${data.settings.currency.symbol}${data.costPerUnit.toFixed(3)}`;
        }

        if (currentUnitsElement) {
            currentUnitsElement.textContent = `${data.currentUnits.toFixed(1)}`;
        }

        if (newTotalElement) {
            newTotalElement.textContent = `${data.newTotal.toFixed(1)}`;
        }
    }

    savePurchase() {
        const currencyInput = document.getElementById('purchase-amount');
        const unitsInput = document.getElementById('purchase-units');

        const currencyValue = parseFloat(currencyInput?.value || 0);
        const unitsValue = parseFloat(unitsInput?.value || 0);

        if (currencyValue <= 0 || unitsValue <= 0) {
            this.showMessage('Please enter valid currency and units values.', 'error');
            return;
        }

        const currentUnits = StorageManager.getCurrentUnits();
        const newTotal = currentUnits + unitsValue;
        const costPerUnit = currencyValue / unitsValue;

        // Save purchase
        const purchase = StorageManager.addPurchase({
            currency: currencyValue,
            units: unitsValue,
            costPerUnit: costPerUnit,
            previousUnits: currentUnits,
            newTotal: newTotal
        });

        if (purchase) {
            // Update current units
            StorageManager.setCurrentUnits(newTotal);

            // Update cost per unit setting if significantly different
            const settings = StorageManager.getSettings();
            if (Math.abs(costPerUnit - settings.costPerUnit) > 0.01) {
                StorageManager.updateSetting('costPerUnit', costPerUnit);
            }

            // Clear form and refresh
            this.clearForm();
            this.loadRecentPurchases();

            // Refresh dashboard if available
            if (window.dashboardManager) {
                window.dashboardManager.refresh();
            }

            this.showMessage(`Purchase saved! Added ${unitsValue} units for ${settings.currency.symbol}${currencyValue.toFixed(2)}`, 'success');
        } else {
            this.showMessage('Failed to save purchase. Please try again.', 'error');
        }
    }

    clearForm() {
        const currencyInput = document.getElementById('purchase-amount');
        const unitsInput = document.getElementById('purchase-units');

        if (currencyInput) currencyInput.value = '';
        if (unitsInput) unitsInput.value = '';

        this.updateLiveCalculation();
    }

    loadRecentPurchases() {
        const purchases = StorageManager.getPurchases().slice(0, 5); // Last 5 purchases
        const purchasesList = document.getElementById('purchases-list');

        if (!purchasesList) return;

        if (purchases.length === 0) {
            purchasesList.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">📝</div>
                    <div class="empty-state-text">No purchases recorded yet</div>
                    <div class="empty-state-subtext">Add your first purchase above</div>
                </div>
            `;
            return;
        }

        const settings = StorageManager.getSettings();

        purchasesList.innerHTML = purchases.map(purchase => `
            <div class="purchase-item">
                <div class="item-info">
                    <div class="item-title">
                        ${settings.currency.symbol}${purchase.currency.toFixed(2)} → ${purchase.units.toFixed(1)} ${settings.unit.name}
                    </div>
                    <div class="item-details">
                        <span>${new Date(purchase.timestamp).toLocaleDateString()}</span>
                        <span>${new Date(purchase.timestamp).toLocaleTimeString()}</span>
                        <span>${settings.currency.symbol}${purchase.costPerUnit.toFixed(3)}/unit</span>
                    </div>
                </div>
                <div class="item-amount">
                    ${settings.currency.symbol}${purchase.currency.toFixed(2)}
                </div>
            </div>
        `).join('');
    }

    deletePurchase(id) {
        if (confirm('Are you sure you want to delete this purchase?')) {
            if (StorageManager.deletePurchase(id)) {
                this.loadRecentPurchases();
                this.showMessage('Purchase deleted successfully.', 'success');
            } else {
                this.showMessage('Failed to delete purchase.', 'error');
            }
        }
    }

    showMessage(message, type = 'info') {
        // Simple message display - can be enhanced with a proper notification system
        const messageClass = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
        console.log(`${type.toUpperCase()}: ${message}`);
        
        // You could implement a toast notification here
        if (window.electricityApp) {
            window.electricityApp.showNotification(message, type);
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PurchasesManager;
}
