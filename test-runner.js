/**
 * Automated Test Runner for Prepaid Electricity App
 * This script can be run in the browser console or via automation tools
 */

class AutomatedTestRunner {
    constructor() {
        this.results = [];
        this.startTime = Date.now();
    }

    async runAllTests() {
        console.log('🧪 Starting Automated Test Suite...');
        
        // Wait for app to be fully loaded
        await this.waitForApp();
        
        // Run all test categories
        await this.testAppInitialization();
        await this.testPageNavigation();
        await this.testStorageSystem();
        await this.testPurchaseSystem();
        await this.testUsageSystem();
        await this.testSettingsSystem();
        
        // Generate report
        this.generateReport();
        
        return this.results;
    }

    async waitForApp() {
        return new Promise((resolve) => {
            const checkApp = () => {
                if (window.electricityApp && document.getElementById('app')) {
                    resolve();
                } else {
                    setTimeout(checkApp, 100);
                }
            };
            checkApp();
        });
    }

    addResult(category, test, passed, message, details = null) {
        this.results.push({
            category,
            test,
            passed,
            message,
            details,
            timestamp: Date.now()
        });
        
        const status = passed ? '✅' : '❌';
        console.log(`${status} [${category}] ${test}: ${message}`);
    }

    async testAppInitialization() {
        console.log('\n📱 Testing App Initialization...');
        
        // Test main app object
        this.addResult('initialization', 'Main App Object', 
            !!window.electricityApp, 
            window.electricityApp ? 'App object exists' : 'App object missing');
        
        // Test storage manager
        this.addResult('initialization', 'Storage Manager', 
            typeof StorageManager !== 'undefined', 
            typeof StorageManager !== 'undefined' ? 'StorageManager available' : 'StorageManager missing');
        
        // Test app container
        const appContainer = document.getElementById('app');
        this.addResult('initialization', 'App Container', 
            !!appContainer, 
            appContainer ? 'App container found' : 'App container missing');
        
        // Test loading screen removal
        const loadingScreen = document.getElementById('loading-screen');
        const isHidden = loadingScreen && (loadingScreen.style.display === 'none' || !loadingScreen.offsetParent);
        this.addResult('initialization', 'Loading Screen', 
            isHidden, 
            isHidden ? 'Loading screen hidden' : 'Loading screen still visible');
    }

    async testPageNavigation() {
        console.log('\n🧭 Testing Page Navigation...');
        
        const pages = ['dashboard', 'purchases', 'usage', 'history', 'settings'];
        
        for (const page of pages) {
            // Test page element exists
            const pageElement = document.getElementById(`${page}-page`);
            this.addResult('navigation', `${page} Page Element`, 
                !!pageElement, 
                pageElement ? 'Page element found' : 'Page element missing');
            
            // Test navigation link exists
            const navLink = document.querySelector(`[data-page="${page}"]`);
            this.addResult('navigation', `${page} Nav Link`, 
                !!navLink, 
                navLink ? 'Navigation link found' : 'Navigation link missing');
            
            // Test page switching
            if (window.electricityApp && window.electricityApp.showPage) {
                try {
                    window.electricityApp.showPage(page);
                    await new Promise(resolve => setTimeout(resolve, 100)); // Wait for transition
                    
                    const activePage = document.querySelector('.page.active');
                    const isCorrectPage = activePage && activePage.id === `${page}-page`;
                    
                    this.addResult('navigation', `${page} Page Switch`, 
                        isCorrectPage, 
                        isCorrectPage ? 'Page switching works' : 'Page switching failed');
                } catch (error) {
                    this.addResult('navigation', `${page} Page Switch`, 
                        false, 
                        `Page switching error: ${error.message}`);
                }
            }
        }
        
        // Return to dashboard
        if (window.electricityApp && window.electricityApp.showPage) {
            window.electricityApp.showPage('dashboard');
        }
    }

    async testStorageSystem() {
        console.log('\n💾 Testing Storage System...');
        
        if (typeof StorageManager === 'undefined') {
            this.addResult('storage', 'Storage Manager', false, 'StorageManager not available');
            return;
        }
        
        try {
            // Test current units
            const originalUnits = StorageManager.getCurrentUnits();
            const testValue = 123.45;
            
            StorageManager.setCurrentUnits(testValue);
            const retrievedValue = StorageManager.getCurrentUnits();
            
            this.addResult('storage', 'Units Storage', 
                retrievedValue === testValue, 
                `Set ${testValue}, got ${retrievedValue}`);
            
            // Restore original value
            StorageManager.setCurrentUnits(originalUnits);
            
            // Test settings
            const settings = StorageManager.getSettings();
            this.addResult('storage', 'Settings Retrieval', 
                settings && typeof settings === 'object', 
                settings ? 'Settings object retrieved' : 'Settings retrieval failed');
            
            // Test purchases
            const purchases = StorageManager.getPurchases();
            this.addResult('storage', 'Purchases Retrieval', 
                Array.isArray(purchases), 
                Array.isArray(purchases) ? `${purchases.length} purchases found` : 'Purchases retrieval failed');
            
            // Test usage records
            const usageRecords = StorageManager.getUsageRecords();
            this.addResult('storage', 'Usage Records Retrieval', 
                Array.isArray(usageRecords), 
                Array.isArray(usageRecords) ? `${usageRecords.length} usage records found` : 'Usage records retrieval failed');
            
        } catch (error) {
            this.addResult('storage', 'Storage Operations', false, `Storage error: ${error.message}`);
        }
    }

    async testPurchaseSystem() {
        console.log('\n💰 Testing Purchase System...');
        
        // Test purchase form elements
        const form = document.getElementById('purchase-form');
        const amountInput = document.getElementById('purchase-amount');
        const unitsInput = document.getElementById('purchase-units');
        const currencySymbol = document.getElementById('purchase-currency-symbol');
        
        this.addResult('purchases', 'Form Elements', 
            !!(form && amountInput && unitsInput && currencySymbol), 
            'Purchase form elements ' + (form && amountInput && unitsInput && currencySymbol ? 'found' : 'missing'));
        
        // Test purchases manager
        this.addResult('purchases', 'Purchases Manager', 
            typeof window.purchasesManager !== 'undefined', 
            typeof window.purchasesManager !== 'undefined' ? 'PurchasesManager loaded' : 'PurchasesManager missing');
        
        // Test form interaction
        if (amountInput && unitsInput) {
            try {
                const originalAmount = amountInput.value;
                const originalUnits = unitsInput.value;
                
                amountInput.value = '25.00';
                unitsInput.value = '100';
                
                // Trigger input event
                amountInput.dispatchEvent(new Event('input'));
                
                this.addResult('purchases', 'Form Interaction', true, 'Form input events work');
                
                // Restore values
                amountInput.value = originalAmount;
                unitsInput.value = originalUnits;
            } catch (error) {
                this.addResult('purchases', 'Form Interaction', false, `Form interaction error: ${error.message}`);
            }
        }
    }

    async testUsageSystem() {
        console.log('\n📈 Testing Usage System...');
        
        // Test usage form elements
        const form = document.getElementById('usage-form');
        const newUnitsInput = document.getElementById('new-units');
        const currentUnitsDisplay = document.getElementById('current-units-display');
        const usageChart = document.getElementById('usage-chart');
        
        this.addResult('usage', 'Form Elements', 
            !!(form && newUnitsInput && currentUnitsDisplay), 
            'Usage form elements ' + (form && newUnitsInput && currentUnitsDisplay ? 'found' : 'missing'));
        
        this.addResult('usage', 'Chart Element', 
            !!usageChart, 
            usageChart ? 'Usage chart element found' : 'Usage chart element missing');
        
        // Test usage manager
        this.addResult('usage', 'Usage Manager', 
            typeof window.usageManager !== 'undefined', 
            typeof window.usageManager !== 'undefined' ? 'UsageManager loaded' : 'UsageManager missing');
    }

    async testSettingsSystem() {
        console.log('\n⚙️ Testing Settings System...');
        
        // Test settings form elements
        const currencySelect = document.getElementById('currency-select');
        const unitSelect = document.getElementById('unit-select');
        const costPerUnit = document.getElementById('cost-per-unit');
        const themeSelect = document.getElementById('theme-select');
        
        this.addResult('settings', 'Form Elements', 
            !!(currencySelect && unitSelect && costPerUnit && themeSelect), 
            'Settings form elements ' + (currencySelect && unitSelect && costPerUnit && themeSelect ? 'found' : 'missing'));
        
        // Test settings manager
        this.addResult('settings', 'Settings Manager', 
            typeof window.settingsManager !== 'undefined', 
            typeof window.settingsManager !== 'undefined' ? 'SettingsManager loaded' : 'SettingsManager missing');
        
        // Test theme manager
        this.addResult('settings', 'Theme Manager', 
            typeof ThemeManager !== 'undefined', 
            typeof ThemeManager !== 'undefined' ? 'ThemeManager loaded' : 'ThemeManager missing');
    }

    generateReport() {
        const endTime = Date.now();
        const duration = endTime - this.startTime;
        
        const totalTests = this.results.length;
        const passedTests = this.results.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;
        
        console.log('\n' + '='.repeat(60));
        console.log('🧪 AUTOMATED TEST REPORT');
        console.log('='.repeat(60));
        console.log(`⏱️  Duration: ${duration}ms`);
        console.log(`📊 Total Tests: ${totalTests}`);
        console.log(`✅ Passed: ${passedTests}`);
        console.log(`❌ Failed: ${failedTests}`);
        console.log(`📈 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
        console.log('='.repeat(60));
        
        // Group results by category
        const categories = {};
        this.results.forEach(result => {
            if (!categories[result.category]) {
                categories[result.category] = [];
            }
            categories[result.category].push(result);
        });
        
        // Print category summaries
        Object.keys(categories).forEach(category => {
            const categoryResults = categories[category];
            const categoryPassed = categoryResults.filter(r => r.passed).length;
            const categoryTotal = categoryResults.length;
            
            console.log(`\n📂 ${category.toUpperCase()}: ${categoryPassed}/${categoryTotal} passed`);
            
            categoryResults.forEach(result => {
                const status = result.passed ? '✅' : '❌';
                console.log(`  ${status} ${result.test}: ${result.message}`);
            });
        });
        
        console.log('\n' + '='.repeat(60));
        
        return {
            duration,
            totalTests,
            passedTests,
            failedTests,
            successRate: (passedTests / totalTests) * 100,
            results: this.results,
            categories
        };
    }
}

// Make it available globally
window.AutomatedTestRunner = AutomatedTestRunner;

// Auto-run if in test mode
if (window.location.search.includes('autotest=true')) {
    document.addEventListener('DOMContentLoaded', async () => {
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for app to fully load
        const runner = new AutomatedTestRunner();
        await runner.runAllTests();
    });
}
