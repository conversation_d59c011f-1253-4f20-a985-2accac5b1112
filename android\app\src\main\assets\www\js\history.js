// History Manager - Handles comprehensive history logging and analytics
class HistoryManager {
    constructor() {
        this.currentView = 'all';
        this.currentFilter = 'all';
        this.init();
    }

    init() {
        this.createHistoryPage();
        this.setupEventListeners();
        this.refresh();
    }

    createHistoryPage() {
        const historyPage = document.getElementById('history-page');
        if (!historyPage) return;

        historyPage.innerHTML = `
            <div class="page-header">
                <h2><span class="page-icon">📋</span>History</h2>
                <p class="page-description">View detailed logs of all purchases and usage patterns</p>
            </div>

            <!-- History Summary Cards -->
            <div class="history-summary">
                <div class="summary-card-large">
                    <div class="summary-header">
                        <span class="summary-icon">📊</span>
                        <h3>Total Summary</h3>
                    </div>
                    <div class="summary-stats">
                        <div class="summary-stat">
                            <span class="stat-label">Total Purchases:</span>
                            <span class="stat-value" id="total-purchases">0</span>
                        </div>
                        <div class="summary-stat">
                            <span class="stat-label">Total Spent:</span>
                            <span class="stat-value" id="total-spent">$0.00</span>
                        </div>
                        <div class="summary-stat">
                            <span class="stat-label">Total Usage:</span>
                            <span class="stat-value" id="total-usage">0.0 Units</span>
                        </div>
                        <div class="summary-stat">
                            <span class="stat-label">Usage Records:</span>
                            <span class="stat-value" id="total-usage-records">0</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Period Analysis -->
            <div class="period-analysis">
                <h3><span class="section-icon">📈</span>Period Analysis</h3>
                <div class="period-tabs">
                    <button class="period-tab active" data-period="week">This Week</button>
                    <button class="period-tab" data-period="month">This Month</button>
                    <button class="period-tab" data-period="year">This Year</button>
                </div>
                <div class="period-content">
                    <div class="period-stats">
                        <div class="period-stat-card">
                            <div class="period-stat-icon">💰</div>
                            <div class="period-stat-content">
                                <div class="period-stat-label">Purchases</div>
                                <div class="period-stat-value" id="period-purchases">0</div>
                                <div class="period-stat-amount" id="period-purchase-amount">$0.00</div>
                            </div>
                        </div>
                        <div class="period-stat-card">
                            <div class="period-stat-icon">⚡</div>
                            <div class="period-stat-content">
                                <div class="period-stat-label">Usage</div>
                                <div class="period-stat-value" id="period-usage">0.0</div>
                                <div class="period-stat-amount" id="period-usage-cost">$0.00</div>
                            </div>
                        </div>
                        <div class="period-stat-card">
                            <div class="period-stat-icon">📊</div>
                            <div class="period-stat-content">
                                <div class="period-stat-label">Net Change</div>
                                <div class="period-stat-value" id="period-net-change">0.0</div>
                                <div class="period-stat-amount" id="period-net-cost">$0.00</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- History Filters -->
            <div class="history-filters">
                <h3><span class="section-icon">🔍</span>Filter History</h3>
                <div class="filter-controls">
                    <div class="filter-group">
                        <label for="history-type-filter">Type:</label>
                        <select id="history-type-filter" class="filter-select">
                            <option value="all">All Records</option>
                            <option value="purchases">Purchases Only</option>
                            <option value="usage">Usage Only</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="history-date-filter">Period:</label>
                        <select id="history-date-filter" class="filter-select">
                            <option value="all">All Time</option>
                            <option value="today">Today</option>
                            <option value="week">This Week</option>
                            <option value="month">This Month</option>
                            <option value="year">This Year</option>
                        </select>
                    </div>
                    <button id="export-history-btn" class="btn-secondary">
                        <span class="btn-icon">📤</span>Export Data
                    </button>
                </div>
            </div>

            <!-- History Timeline -->
            <div class="history-timeline">
                <h3><span class="section-icon">📅</span>Timeline</h3>
                <div id="history-timeline-content" class="timeline-content">
                    <!-- Timeline items will be populated here -->
                </div>
            </div>

            <!-- Detailed History Table -->
            <div class="history-table-container">
                <h3><span class="section-icon">📋</span>Detailed Records</h3>
                <div class="table-wrapper">
                    <table class="history-table">
                        <thead>
                            <tr>
                                <th>Date & Time</th>
                                <th>Type</th>
                                <th>Details</th>
                                <th>Amount</th>
                                <th>Cost</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="history-table-body">
                            <!-- Table rows will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        // Period tabs
        document.querySelectorAll('.period-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                document.querySelectorAll('.period-tab').forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
                this.updatePeriodAnalysis(tab.dataset.period);
            });
        });

        // Filter controls
        const typeFilter = document.getElementById('history-type-filter');
        const dateFilter = document.getElementById('history-date-filter');
        const exportBtn = document.getElementById('export-history-btn');

        typeFilter?.addEventListener('change', () => this.applyFilters());
        dateFilter?.addEventListener('change', () => this.applyFilters());
        exportBtn?.addEventListener('click', () => this.exportHistory());
    }

    refresh() {
        this.updateHistorySummary();
        this.updatePeriodAnalysis('week');
        this.loadHistoryTimeline();
        this.loadHistoryTable();
    }

    updateHistorySummary() {
        const purchases = StorageManager.getPurchases();
        const usageRecords = StorageManager.getUsageRecords();
        const settings = StorageManager.getSettings();

        // Calculate totals
        const totalPurchases = purchases.length;
        const totalSpent = purchases.reduce((sum, p) => sum + p.currency, 0);
        const totalUsage = usageRecords.reduce((sum, r) => sum + r.usage, 0);
        const totalUsageRecords = usageRecords.length;

        // Update display
        const totalPurchasesElement = document.getElementById('total-purchases');
        const totalSpentElement = document.getElementById('total-spent');
        const totalUsageElement = document.getElementById('total-usage');
        const totalUsageRecordsElement = document.getElementById('total-usage-records');

        if (totalPurchasesElement) totalPurchasesElement.textContent = totalPurchases.toString();
        if (totalSpentElement) totalSpentElement.textContent = `${settings.currency.symbol}${totalSpent.toFixed(2)}`;
        if (totalUsageElement) totalUsageElement.textContent = `${totalUsage.toFixed(1)} ${settings.unit.name}`;
        if (totalUsageRecordsElement) totalUsageRecordsElement.textContent = totalUsageRecords.toString();
    }

    updatePeriodAnalysis(period) {
        const now = new Date();
        let startDate;

        switch (period) {
            case 'week':
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case 'month':
                startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                break;
            case 'year':
                startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
                break;
            default:
                startDate = new Date(0);
        }

        const purchases = StorageManager.getPurchases().filter(p => new Date(p.timestamp) >= startDate);
        const usageRecords = StorageManager.getUsageRecords().filter(r => new Date(r.timestamp) >= startDate);
        const settings = StorageManager.getSettings();

        // Calculate period stats
        const periodPurchases = purchases.length;
        const periodPurchaseAmount = purchases.reduce((sum, p) => sum + p.currency, 0);
        const periodUsage = usageRecords.reduce((sum, r) => sum + r.usage, 0);
        const periodUsageCost = periodUsage * settings.costPerUnit;
        const periodNetChange = purchases.reduce((sum, p) => sum + p.units, 0) - periodUsage;
        const periodNetCost = periodPurchaseAmount - periodUsageCost;

        // Update display
        const periodPurchasesElement = document.getElementById('period-purchases');
        const periodPurchaseAmountElement = document.getElementById('period-purchase-amount');
        const periodUsageElement = document.getElementById('period-usage');
        const periodUsageCostElement = document.getElementById('period-usage-cost');
        const periodNetChangeElement = document.getElementById('period-net-change');
        const periodNetCostElement = document.getElementById('period-net-cost');

        if (periodPurchasesElement) periodPurchasesElement.textContent = periodPurchases.toString();
        if (periodPurchaseAmountElement) periodPurchaseAmountElement.textContent = `${settings.currency.symbol}${periodPurchaseAmount.toFixed(2)}`;
        if (periodUsageElement) periodUsageElement.textContent = `${periodUsage.toFixed(1)} ${settings.unit.name}`;
        if (periodUsageCostElement) periodUsageCostElement.textContent = `${settings.currency.symbol}${periodUsageCost.toFixed(2)}`;
        if (periodNetChangeElement) {
            periodNetChangeElement.textContent = `${periodNetChange >= 0 ? '+' : ''}${periodNetChange.toFixed(1)} ${settings.unit.name}`;
            periodNetChangeElement.className = `period-stat-value ${periodNetChange >= 0 ? 'positive' : 'negative'}`;
        }
        if (periodNetCostElement) {
            periodNetCostElement.textContent = `${periodNetCost >= 0 ? '+' : ''}${settings.currency.symbol}${Math.abs(periodNetCost).toFixed(2)}`;
            periodNetCostElement.className = `period-stat-amount ${periodNetCost >= 0 ? 'positive' : 'negative'}`;
        }
    }

    loadHistoryTimeline() {
        const purchases = StorageManager.getPurchases();
        const usageRecords = StorageManager.getUsageRecords();
        const settings = StorageManager.getSettings();

        // Combine and sort all records
        const allRecords = [
            ...purchases.map(p => ({ ...p, type: 'purchase' })),
            ...usageRecords.map(r => ({ ...r, type: 'usage' }))
        ].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

        const timelineContent = document.getElementById('history-timeline-content');
        if (!timelineContent) return;

        if (allRecords.length === 0) {
            timelineContent.innerHTML = `
                <div class="empty-state">
                    <span class="empty-icon">📅</span>
                    <p>No history records yet</p>
                    <small>Start by making a purchase or recording usage</small>
                </div>
            `;
            return;
        }

        timelineContent.innerHTML = allRecords.slice(0, 10).map(record => {
            const date = new Date(record.timestamp);
            const isToday = date.toDateString() === new Date().toDateString();
            
            if (record.type === 'purchase') {
                return `
                    <div class="timeline-item purchase">
                        <div class="timeline-icon">💰</div>
                        <div class="timeline-content">
                            <div class="timeline-header">
                                <span class="timeline-type">Purchase</span>
                                <span class="timeline-date ${isToday ? 'today' : ''}">${isToday ? 'Today' : date.toLocaleDateString()}</span>
                            </div>
                            <div class="timeline-details">
                                Added ${record.units.toFixed(1)} ${settings.unit.name} for ${settings.currency.symbol}${record.currency.toFixed(2)}
                            </div>
                            <div class="timeline-time">${date.toLocaleTimeString()}</div>
                        </div>
                    </div>
                `;
            } else {
                return `
                    <div class="timeline-item usage">
                        <div class="timeline-icon">⚡</div>
                        <div class="timeline-content">
                            <div class="timeline-header">
                                <span class="timeline-type">Usage</span>
                                <span class="timeline-date ${isToday ? 'today' : ''}">${isToday ? 'Today' : date.toLocaleDateString()}</span>
                            </div>
                            <div class="timeline-details">
                                Used ${record.usage.toFixed(1)} ${settings.unit.name} (${record.previousUnits.toFixed(1)} → ${record.currentUnits.toFixed(1)})
                            </div>
                            <div class="timeline-time">${date.toLocaleTimeString()}</div>
                        </div>
                    </div>
                `;
            }
        }).join('');
    }

    loadHistoryTable() {
        const purchases = StorageManager.getPurchases();
        const usageRecords = StorageManager.getUsageRecords();
        const settings = StorageManager.getSettings();

        // Combine and sort all records
        const allRecords = [
            ...purchases.map(p => ({ ...p, type: 'purchase' })),
            ...usageRecords.map(r => ({ ...r, type: 'usage' }))
        ].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

        const tableBody = document.getElementById('history-table-body');
        if (!tableBody) return;

        if (allRecords.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="6" class="empty-table">
                        <div class="empty-state">
                            <span class="empty-icon">📋</span>
                            <p>No records found</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        tableBody.innerHTML = allRecords.map(record => {
            const date = new Date(record.timestamp);
            
            if (record.type === 'purchase') {
                return `
                    <tr class="table-row purchase-row">
                        <td class="date-cell">
                            <div class="date-display">
                                <span class="date">${date.toLocaleDateString()}</span>
                                <span class="time">${date.toLocaleTimeString()}</span>
                            </div>
                        </td>
                        <td class="type-cell">
                            <span class="type-badge purchase">💰 Purchase</span>
                        </td>
                        <td class="details-cell">
                            Added ${record.units.toFixed(1)} ${settings.unit.name}<br>
                            <small>Rate: ${settings.currency.symbol}${record.costPerUnit.toFixed(3)}/unit</small>
                        </td>
                        <td class="amount-cell">
                            +${record.units.toFixed(1)} ${settings.unit.name}
                        </td>
                        <td class="cost-cell purchase-cost">
                            ${settings.currency.symbol}${record.currency.toFixed(2)}
                        </td>
                        <td class="actions-cell">
                            <button class="delete-btn" onclick="window.historyManager.deleteRecord('purchase', '${record.id}')">🗑️</button>
                        </td>
                    </tr>
                `;
            } else {
                const cost = record.usage * settings.costPerUnit;
                return `
                    <tr class="table-row usage-row">
                        <td class="date-cell">
                            <div class="date-display">
                                <span class="date">${date.toLocaleDateString()}</span>
                                <span class="time">${date.toLocaleTimeString()}</span>
                            </div>
                        </td>
                        <td class="type-cell">
                            <span class="type-badge usage">⚡ Usage</span>
                        </td>
                        <td class="details-cell">
                            Used ${record.usage.toFixed(1)} ${settings.unit.name}<br>
                            <small>Reading: ${record.previousUnits.toFixed(1)} → ${record.currentUnits.toFixed(1)}</small>
                        </td>
                        <td class="amount-cell usage-amount">
                            -${record.usage.toFixed(1)} ${settings.unit.name}
                        </td>
                        <td class="cost-cell usage-cost">
                            ${settings.currency.symbol}${cost.toFixed(2)}
                        </td>
                        <td class="actions-cell">
                            <button class="delete-btn" onclick="window.historyManager.deleteRecord('usage', '${record.id}')">🗑️</button>
                        </td>
                    </tr>
                `;
            }
        }).join('');
    }

    applyFilters() {
        const typeFilter = document.getElementById('history-type-filter')?.value || 'all';
        const dateFilter = document.getElementById('history-date-filter')?.value || 'all';
        
        this.currentFilter = { type: typeFilter, date: dateFilter };
        
        // For now, just reload the table - in a more advanced implementation,
        // you would filter the data before displaying
        this.loadHistoryTable();
        this.loadHistoryTimeline();
    }

    deleteRecord(type, id) {
        if (!confirm(`Are you sure you want to delete this ${type} record?`)) {
            return;
        }

        let success = false;
        if (type === 'purchase') {
            success = StorageManager.deletePurchase(id);
        } else if (type === 'usage') {
            success = StorageManager.deleteUsageRecord(id);
        }

        if (success) {
            this.refresh();
            this.showMessage(`${type} record deleted successfully.`, 'success');
            
            // Refresh other managers
            if (window.dashboardManager) window.dashboardManager.refresh();
            if (window.purchasesManager) window.purchasesManager.refresh();
            if (window.usageManager) window.usageManager.refresh();
        } else {
            this.showMessage(`Failed to delete ${type} record.`, 'error');
        }
    }

    exportHistory() {
        const data = StorageManager.exportData();
        if (!data) {
            this.showMessage('Failed to export data.', 'error');
            return;
        }

        // Create and download file
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `electricity-history-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showMessage('History exported successfully!', 'success');
    }

    showMessage(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
        if (window.electricityApp) {
            window.electricityApp.showNotification(message, type);
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HistoryManager;
}
