// Purchases Manager - Handles purchase recording and calculations
class PurchasesManager {
    constructor() {
        this.currentCurrency = 0;
        this.currentUnits = 0;
        this.isCalculating = false;
        this.init();
    }

    init() {
        this.createPurchasesPage();
        this.setupEventListeners();
        this.refresh();
    }

    createPurchasesPage() {
        const purchasesPage = document.getElementById('purchases-page');
        if (!purchasesPage) return;

        purchasesPage.innerHTML = `
            <div class="page-header">
                <h2><span class="page-icon">💰</span>Purchases</h2>
                <p class="page-description">Record your electricity purchases and see live calculations</p>
            </div>

            <!-- Purchase Form -->
            <div class="purchase-form-container">
                <div class="form-card">
                    <h3><span class="form-icon">💳</span>New Purchase</h3>
                    
                    <div class="input-group">
                        <label for="currency-input">
                            <span class="input-icon">💰</span>
                            Currency Amount
                        </label>
                        <div class="input-wrapper">
                            <span class="currency-symbol" id="currency-symbol">$</span>
                            <input type="number" id="currency-input" placeholder="0.00" step="0.01" min="0">
                        </div>
                    </div>

                    <div class="input-group">
                        <label for="units-input">
                            <span class="input-icon">⚡</span>
                            Units Purchased
                        </label>
                        <div class="input-wrapper">
                            <input type="number" id="units-input" placeholder="0.0" step="0.1" min="0">
                            <span class="units-symbol" id="units-symbol">Units</span>
                        </div>
                    </div>

                    <!-- Live Preview -->
                    <div class="calculation-preview" id="calculation-preview">
                        <h4><span class="preview-icon">📊</span>Live Calculation</h4>
                        <div class="preview-grid">
                            <div class="preview-item">
                                <span class="preview-label">Cost per Unit:</span>
                                <span class="preview-value" id="cost-per-unit-preview">$0.00</span>
                            </div>
                            <div class="preview-item">
                                <span class="preview-label">Total Cost:</span>
                                <span class="preview-value" id="total-cost-preview">$0.00</span>
                            </div>
                            <div class="preview-item">
                                <span class="preview-label">Current Units:</span>
                                <span class="preview-value" id="current-units-preview">0.0 Units</span>
                            </div>
                            <div class="preview-item">
                                <span class="preview-label">New Total:</span>
                                <span class="preview-value highlight" id="new-total-preview">0.0 Units</span>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" id="clear-purchase-btn" class="btn-secondary">
                            <span class="btn-icon">🗑️</span>Clear
                        </button>
                        <button type="button" id="save-purchase-btn" class="btn-primary">
                            <span class="btn-icon">💾</span>Save Purchase
                        </button>
                    </div>
                </div>
            </div>

            <!-- Recent Purchases -->
            <div class="recent-purchases">
                <h3><span class="section-icon">📋</span>Recent Purchases</h3>
                <div id="recent-purchases-list" class="purchases-list">
                    <!-- Recent purchases will be populated here -->
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        const currencyInput = document.getElementById('currency-input');
        const unitsInput = document.getElementById('units-input');
        const savePurchaseBtn = document.getElementById('save-purchase-btn');
        const clearPurchaseBtn = document.getElementById('clear-purchase-btn');

        // Live calculation on input
        currencyInput?.addEventListener('input', () => this.updateLiveCalculation());
        unitsInput?.addEventListener('input', () => this.updateLiveCalculation());

        // Form actions
        savePurchaseBtn?.addEventListener('click', () => this.savePurchase());
        clearPurchaseBtn?.addEventListener('click', () => this.clearForm());

        // Enter key to save
        currencyInput?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') unitsInput?.focus();
        });
        
        unitsInput?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.savePurchase();
        });
    }

    refresh() {
        this.updateCurrencySymbols();
        this.updateUnitsSymbols();
        this.updateLiveCalculation();
        this.loadRecentPurchases();
    }

    updateCurrencySymbols() {
        const settings = StorageManager.getSettings();
        const currencySymbol = settings.currency.symbol;
        
        const currencySymbolElement = document.getElementById('currency-symbol');
        if (currencySymbolElement) {
            currencySymbolElement.textContent = currencySymbol;
        }
    }

    updateUnitsSymbols() {
        const settings = StorageManager.getSettings();
        const unitName = settings.unit.name;
        
        const unitsSymbolElement = document.getElementById('units-symbol');
        if (unitsSymbolElement) {
            unitsSymbolElement.textContent = unitName;
        }
    }

    updateLiveCalculation() {
        if (this.isCalculating) return;
        this.isCalculating = true;

        const currencyInput = document.getElementById('currency-input');
        const unitsInput = document.getElementById('units-input');
        
        const currencyValue = parseFloat(currencyInput?.value || 0);
        const unitsValue = parseFloat(unitsInput?.value || 0);
        
        const settings = StorageManager.getSettings();
        const currentUnits = StorageManager.getCurrentUnits();
        
        // Calculate cost per unit
        let costPerUnit = 0;
        if (unitsValue > 0 && currencyValue > 0) {
            costPerUnit = currencyValue / unitsValue;
        } else if (currencyValue > 0) {
            costPerUnit = settings.costPerUnit;
        }
        
        // Calculate units if only currency is entered
        let calculatedUnits = unitsValue;
        if (currencyValue > 0 && unitsValue === 0) {
            calculatedUnits = currencyValue / settings.costPerUnit;
            if (unitsInput) {
                unitsInput.value = calculatedUnits.toFixed(1);
            }
        }
        
        // Calculate currency if only units are entered
        let calculatedCurrency = currencyValue;
        if (unitsValue > 0 && currencyValue === 0) {
            calculatedCurrency = unitsValue * settings.costPerUnit;
            if (currencyInput) {
                currencyInput.value = calculatedCurrency.toFixed(2);
            }
        }
        
        const newTotal = currentUnits + (calculatedUnits || unitsValue);
        
        // Update preview
        this.updatePreviewDisplay({
            costPerUnit: costPerUnit,
            totalCost: calculatedCurrency || currencyValue,
            currentUnits: currentUnits,
            newTotal: newTotal,
            settings: settings
        });

        this.isCalculating = false;
    }

    updatePreviewDisplay(data) {
        const costPerUnitElement = document.getElementById('cost-per-unit-preview');
        const totalCostElement = document.getElementById('total-cost-preview');
        const currentUnitsElement = document.getElementById('current-units-preview');
        const newTotalElement = document.getElementById('new-total-preview');
        
        if (costPerUnitElement) {
            costPerUnitElement.textContent = `${data.settings.currency.symbol}${data.costPerUnit.toFixed(3)}`;
        }
        
        if (totalCostElement) {
            totalCostElement.textContent = `${data.settings.currency.symbol}${data.totalCost.toFixed(2)}`;
        }
        
        if (currentUnitsElement) {
            currentUnitsElement.textContent = `${data.currentUnits.toFixed(1)} ${data.settings.unit.name}`;
        }
        
        if (newTotalElement) {
            newTotalElement.textContent = `${data.newTotal.toFixed(1)} ${data.settings.unit.name}`;
        }
    }

    savePurchase() {
        const currencyInput = document.getElementById('currency-input');
        const unitsInput = document.getElementById('units-input');
        
        const currencyValue = parseFloat(currencyInput?.value || 0);
        const unitsValue = parseFloat(unitsInput?.value || 0);
        
        if (currencyValue <= 0 || unitsValue <= 0) {
            this.showMessage('Please enter valid currency and units values.', 'error');
            return;
        }
        
        const currentUnits = StorageManager.getCurrentUnits();
        const newTotal = currentUnits + unitsValue;
        const costPerUnit = currencyValue / unitsValue;
        
        // Save purchase
        const purchase = StorageManager.addPurchase({
            currency: currencyValue,
            units: unitsValue,
            costPerUnit: costPerUnit,
            previousUnits: currentUnits,
            newTotal: newTotal
        });
        
        if (purchase) {
            // Update current units
            StorageManager.setCurrentUnits(newTotal);
            
            // Update cost per unit setting if significantly different
            const settings = StorageManager.getSettings();
            if (Math.abs(costPerUnit - settings.costPerUnit) > 0.01) {
                StorageManager.updateSetting('costPerUnit', costPerUnit);
            }
            
            // Clear form and refresh
            this.clearForm();
            this.loadRecentPurchases();
            
            // Refresh dashboard if available
            if (window.dashboardManager) {
                window.dashboardManager.refresh();
            }
            
            this.showMessage(`Purchase saved! Added ${unitsValue} units for ${settings.currency.symbol}${currencyValue.toFixed(2)}`, 'success');
        } else {
            this.showMessage('Failed to save purchase. Please try again.', 'error');
        }
    }

    clearForm() {
        const currencyInput = document.getElementById('currency-input');
        const unitsInput = document.getElementById('units-input');
        
        if (currencyInput) currencyInput.value = '';
        if (unitsInput) unitsInput.value = '';
        
        this.updateLiveCalculation();
    }

    loadRecentPurchases() {
        const purchases = StorageManager.getPurchases().slice(0, 5); // Last 5 purchases
        const purchasesList = document.getElementById('recent-purchases-list');
        
        if (!purchasesList) return;
        
        if (purchases.length === 0) {
            purchasesList.innerHTML = `
                <div class="empty-state">
                    <span class="empty-icon">📝</span>
                    <p>No purchases recorded yet</p>
                    <small>Add your first purchase above</small>
                </div>
            `;
            return;
        }
        
        const settings = StorageManager.getSettings();
        
        purchasesList.innerHTML = purchases.map(purchase => `
            <div class="purchase-item">
                <div class="purchase-info">
                    <div class="purchase-amount">
                        ${settings.currency.symbol}${purchase.currency.toFixed(2)} → ${purchase.units.toFixed(1)} ${settings.unit.name}
                    </div>
                    <div class="purchase-details">
                        <span class="purchase-date">${new Date(purchase.timestamp).toLocaleDateString()}</span>
                        <span class="purchase-time">${new Date(purchase.timestamp).toLocaleTimeString()}</span>
                        <span class="purchase-rate">${settings.currency.symbol}${purchase.costPerUnit.toFixed(3)}/unit</span>
                    </div>
                </div>
                <button class="delete-purchase-btn" onclick="window.purchasesManager.deletePurchase('${purchase.id}')">
                    🗑️
                </button>
            </div>
        `).join('');
    }

    deletePurchase(id) {
        if (confirm('Are you sure you want to delete this purchase?')) {
            if (StorageManager.deletePurchase(id)) {
                this.loadRecentPurchases();
                this.showMessage('Purchase deleted successfully.', 'success');
            } else {
                this.showMessage('Failed to delete purchase.', 'error');
            }
        }
    }

    showMessage(message, type = 'info') {
        // Simple message display - can be enhanced with a proper notification system
        const messageClass = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
        console.log(`${type.toUpperCase()}: ${message}`);
        
        // You could implement a toast notification here
        if (window.electricityApp) {
            window.electricityApp.showNotification(message, type);
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PurchasesManager;
}
