<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>App Test Launcher</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            text-align: center;
        }
        h1 {
            margin-bottom: 30px;
            font-size: 2.5rem;
        }
        .test-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .test-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 25px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .test-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        .test-card h3 {
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        .test-card p {
            margin-bottom: 20px;
            opacity: 0.9;
            line-height: 1.5;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            width: 100%;
        }
        button:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        .primary-btn {
            background: #28a745;
            font-size: 18px;
            padding: 15px 30px;
            margin: 20px 0;
        }
        .primary-btn:hover {
            background: #1e7e34;
        }
        .info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #17a2b8;
        }
        .warning {
            background: rgba(255, 193, 7, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 App Test Launcher</h1>
        <p>Choose how you want to test the Prepaid Electricity App:</p>
        
        <button class="primary-btn" onclick="launchMainAppWithTests()">
            🚀 Launch Main App with Test Mode
        </button>
        
        <div class="test-options">
            <div class="test-card">
                <h3>📊 Page Navigation Test</h3>
                <p>Test page switching, navigation links, and page content availability.</p>
                <button onclick="openTestPage('test-pages.html')">Run Navigation Tests</button>
            </div>
            
            <div class="test-card">
                <h3>💰 Purchases Test</h3>
                <p>Test purchase form functionality, calculations, and data storage.</p>
                <button onclick="openTestPage('test-functionality.html#purchases')">Run Purchase Tests</button>
            </div>
            
            <div class="test-card">
                <h3>📈 Usage Test</h3>
                <p>Test usage recording, analytics, and chart functionality.</p>
                <button onclick="openTestPage('test-functionality.html#usage')">Run Usage Tests</button>
            </div>
            
            <div class="test-card">
                <h3>💾 Storage Test</h3>
                <p>Test data persistence, localStorage operations, and data integrity.</p>
                <button onclick="openTestPage('test-functionality.html#storage')">Run Storage Tests</button>
            </div>
        </div>
        
        <div class="info">
            <h4>ℹ️ About Test Mode</h4>
            <p>Test mode adds a floating test panel to the main app that allows you to run tests in the actual app context. This provides more accurate testing of navigation, form interactions, and app state.</p>
        </div>
        
        <div class="warning">
            <h4>⚠️ Note</h4>
            <p>Some tests may modify app data. Use the "Clear Storage" option in test mode to reset data if needed.</p>
        </div>
        
        <div style="margin-top: 30px;">
            <button onclick="openMainApp()">📱 Open Main App (Normal Mode)</button>
            <button onclick="openDebugPage()" style="background: #6c757d; margin-left: 10px;">🔧 Open Debug Page</button>
        </div>
    </div>

    <script>
        function launchMainAppWithTests() {
            // Open main app with test mode enabled
            window.open('index.html?test=true', '_blank');
        }
        
        function openMainApp() {
            // Open main app in normal mode
            window.open('index.html', '_blank');
        }
        
        function openTestPage(page) {
            // Open specific test page
            window.open(page, '_blank');
        }
        
        function openDebugPage() {
            // Open debug page if it exists
            window.open('debug.html', '_blank');
        }
        
        // Auto-launch test mode if requested
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('auto') === 'true') {
            setTimeout(() => {
                launchMainAppWithTests();
            }, 1000);
        }
    </script>
</body>
</html>
