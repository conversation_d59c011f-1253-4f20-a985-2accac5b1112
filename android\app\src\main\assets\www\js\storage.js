// Storage Manager for handling all data persistence
class StorageManager {
    static STORAGE_KEYS = {
        SETTINGS: 'electricity_app_settings',
        PURCHASES: 'electricity_app_purchases',
        USAGE_RECORDS: 'electricity_app_usage',
        CURRENT_UNITS: 'electricity_app_current_units',
        INITIALIZED: 'electricity_app_initialized',
        THEME: 'electricity_app_theme'
    };

    static DEFAULT_SETTINGS = {
        currency: {
            name: 'USD',
            symbol: '$',
            fullName: 'US Dollar',
            isCustom: false,
            customName: '',
            customSymbol: ''
        },
        unit: {
            name: 'Units',
            isCustom: false,
            customName: ''
        },
        costPerUnit: 0.15,
        lowUnitsThreshold: 10,
        notifications: {
            enabled: true,
            time: '18:00',
            message: 'Don\'t forget to record your electricity usage!'
        },
        theme: 'electric-blue',
        fontSize: 'medium',
        colorScheme: 'default'
    };

    static DEFAULT_CURRENCIES = [
        { name: 'USD', symbol: '$', fullName: 'US Dollar' },
        { name: 'EUR', symbol: '€', fullName: 'Euro' },
        { name: 'GBP', symbol: '£', fullName: 'British Pound' },
        { name: '<PERSON><PERSON>', symbol: 'R', fullName: 'South African Rand' },
        { name: 'AUD', symbol: 'A$', fullName: 'Australian Dollar' },
        { name: 'CUSTOM', symbol: '$', fullName: 'Custom Currency', isCustom: true }
    ];

    static DEFAULT_UNITS = [
        { name: 'Units' },
        { name: 'KWh' }
    ];

    static async init() {
        try {
            // Check if we need to migrate old data
            await this.migrateDataIfNeeded();
            
            // Initialize default settings if not exists
            if (!this.getSettings()) {
                this.saveSettings(this.DEFAULT_SETTINGS);
            }
            
            console.log('Storage Manager initialized');
            return true;
        } catch (error) {
            console.error('Failed to initialize storage:', error);
            return false;
        }
    }

    static isInitialized() {
        return localStorage.getItem(this.STORAGE_KEYS.INITIALIZED) === 'true';
    }

    static setInitialized(value = true) {
        localStorage.setItem(this.STORAGE_KEYS.INITIALIZED, value.toString());
    }

    // Settings Management
    static getSettings() {
        try {
            const settings = localStorage.getItem(this.STORAGE_KEYS.SETTINGS);
            return settings ? JSON.parse(settings) : null;
        } catch (error) {
            console.error('Error getting settings:', error);
            return this.DEFAULT_SETTINGS;
        }
    }

    static saveSettings(settings) {
        try {
            localStorage.setItem(this.STORAGE_KEYS.SETTINGS, JSON.stringify(settings));
            return true;
        } catch (error) {
            console.error('Error saving settings:', error);
            return false;
        }
    }

    static updateSetting(key, value) {
        const settings = this.getSettings() || this.DEFAULT_SETTINGS;
        
        // Handle nested keys like 'currency.name'
        const keys = key.split('.');
        let current = settings;
        
        for (let i = 0; i < keys.length - 1; i++) {
            if (!current[keys[i]]) {
                current[keys[i]] = {};
            }
            current = current[keys[i]];
        }
        
        current[keys[keys.length - 1]] = value;
        
        return this.saveSettings(settings);
    }

    // Current Units Management
    static getCurrentUnits() {
        try {
            const units = localStorage.getItem(this.STORAGE_KEYS.CURRENT_UNITS);
            return units ? parseFloat(units) : 0;
        } catch (error) {
            console.error('Error getting current units:', error);
            return 0;
        }
    }

    static setCurrentUnits(units) {
        try {
            localStorage.setItem(this.STORAGE_KEYS.CURRENT_UNITS, units.toString());
            return true;
        } catch (error) {
            console.error('Error setting current units:', error);
            return false;
        }
    }

    // Purchases Management
    static getPurchases() {
        try {
            const purchases = localStorage.getItem(this.STORAGE_KEYS.PURCHASES);
            return purchases ? JSON.parse(purchases) : [];
        } catch (error) {
            console.error('Error getting purchases:', error);
            return [];
        }
    }

    static addPurchase(purchase) {
        try {
            const purchases = this.getPurchases();
            const newPurchase = {
                id: Date.now().toString(),
                timestamp: new Date().toISOString(),
                ...purchase
            };
            
            purchases.unshift(newPurchase); // Add to beginning
            localStorage.setItem(this.STORAGE_KEYS.PURCHASES, JSON.stringify(purchases));
            
            return newPurchase;
        } catch (error) {
            console.error('Error adding purchase:', error);
            return null;
        }
    }

    static deletePurchase(id) {
        try {
            const purchases = this.getPurchases();
            const filtered = purchases.filter(p => p.id !== id);
            localStorage.setItem(this.STORAGE_KEYS.PURCHASES, JSON.stringify(filtered));
            return true;
        } catch (error) {
            console.error('Error deleting purchase:', error);
            return false;
        }
    }

    // Usage Records Management
    static getUsageRecords() {
        try {
            const records = localStorage.getItem(this.STORAGE_KEYS.USAGE_RECORDS);
            return records ? JSON.parse(records) : [];
        } catch (error) {
            console.error('Error getting usage records:', error);
            return [];
        }
    }

    static addUsageRecord(record) {
        try {
            const records = this.getUsageRecords();
            const newRecord = {
                id: Date.now().toString(),
                timestamp: new Date().toISOString(),
                ...record
            };
            
            records.unshift(newRecord); // Add to beginning
            localStorage.setItem(this.STORAGE_KEYS.USAGE_RECORDS, JSON.stringify(records));
            
            return newRecord;
        } catch (error) {
            console.error('Error adding usage record:', error);
            return null;
        }
    }

    static deleteUsageRecord(id) {
        try {
            const records = this.getUsageRecords();
            const filtered = records.filter(r => r.id !== id);
            localStorage.setItem(this.STORAGE_KEYS.USAGE_RECORDS, JSON.stringify(filtered));
            return true;
        } catch (error) {
            console.error('Error deleting usage record:', error);
            return false;
        }
    }

    // Analytics and Calculations
    static getUsageAnalytics() {
        const records = this.getUsageRecords();
        const now = new Date();
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        
        const weeklyRecords = records.filter(r => new Date(r.timestamp) >= weekAgo);
        const monthlyRecords = records.filter(r => new Date(r.timestamp) >= monthAgo);
        
        const weeklyUsage = weeklyRecords.reduce((sum, r) => sum + (r.usage || 0), 0);
        const monthlyUsage = monthlyRecords.reduce((sum, r) => sum + (r.usage || 0), 0);
        
        const settings = this.getSettings();
        const costPerUnit = settings?.costPerUnit || 0.15;
        
        return {
            weekly: {
                usage: weeklyUsage,
                cost: weeklyUsage * costPerUnit,
                records: weeklyRecords.length
            },
            monthly: {
                usage: monthlyUsage,
                cost: monthlyUsage * costPerUnit,
                records: monthlyRecords.length
            },
            total: {
                usage: records.reduce((sum, r) => sum + (r.usage || 0), 0),
                records: records.length
            }
        };
    }

    static getLastUsageRecord() {
        const records = this.getUsageRecords();
        return records.length > 0 ? records[0] : null;
    }

    // Data Management
    static exportData() {
        try {
            const data = {
                settings: this.getSettings(),
                purchases: this.getPurchases(),
                usageRecords: this.getUsageRecords(),
                currentUnits: this.getCurrentUnits(),
                exportDate: new Date().toISOString(),
                version: '1.0'
            };
            
            return JSON.stringify(data, null, 2);
        } catch (error) {
            console.error('Error exporting data:', error);
            return null;
        }
    }

    static importData(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            
            if (data.settings) this.saveSettings(data.settings);
            if (data.purchases) localStorage.setItem(this.STORAGE_KEYS.PURCHASES, JSON.stringify(data.purchases));
            if (data.usageRecords) localStorage.setItem(this.STORAGE_KEYS.USAGE_RECORDS, JSON.stringify(data.usageRecords));
            if (data.currentUnits !== undefined) this.setCurrentUnits(data.currentUnits);
            
            return true;
        } catch (error) {
            console.error('Error importing data:', error);
            return false;
        }
    }

    static factoryReset() {
        try {
            // Clear all app data
            Object.values(this.STORAGE_KEYS).forEach(key => {
                localStorage.removeItem(key);
            });
            
            // Reinitialize with defaults
            this.saveSettings(this.DEFAULT_SETTINGS);
            this.setInitialized(false);
            
            return true;
        } catch (error) {
            console.error('Error performing factory reset:', error);
            return false;
        }
    }

    static dashboardReset() {
        try {
            // Clear only dashboard-related data, keep history
            this.setCurrentUnits(0);
            localStorage.removeItem(this.STORAGE_KEYS.USAGE_RECORDS);
            
            return true;
        } catch (error) {
            console.error('Error performing dashboard reset:', error);
            return false;
        }
    }

    // Migration helper for future updates
    static async migrateDataIfNeeded() {
        // This method can be used for future data migrations
        // when the app structure changes
        const version = localStorage.getItem('app_version');
        
        if (!version) {
            // First time user or very old version
            localStorage.setItem('app_version', '1.0');
        }
        
        // Future migrations can be added here
    }

    // Utility methods
    static clearAllData() {
        return this.factoryReset();
    }

    static getDataSize() {
        let total = 0;
        Object.values(this.STORAGE_KEYS).forEach(key => {
            const item = localStorage.getItem(key);
            if (item) {
                total += item.length;
            }
        });
        return total;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StorageManager;
}
