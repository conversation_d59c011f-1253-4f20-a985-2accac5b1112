// Settings Manager - Handles all app configuration and preferences
class SettingsManager {
    constructor() {
        this.currentSection = 'general';
        this.init();
    }

    init() {
        this.createSettingsPage();
        this.setupEventListeners();
        this.refresh();
    }

    createSettingsPage() {
        const settingsPage = document.getElementById('settings-page');
        if (!settingsPage) return;

        settingsPage.innerHTML = `
            <div class="page-header">
                <h2><span class="page-icon">⚙️</span>Settings</h2>
                <p class="page-description">Configure your app preferences and customize your experience</p>
            </div>

            <!-- Settings Navigation -->
            <div class="settings-nav">
                <button class="settings-nav-btn active" data-section="general">
                    <span class="nav-btn-icon">🔧</span>
                    General Settings
                </button>
                <button class="settings-nav-btn" data-section="appearance">
                    <span class="nav-btn-icon">🎨</span>
                    Appearance
                </button>
                <button class="settings-nav-btn" data-section="reset">
                    <span class="nav-btn-icon">🔄</span>
                    Reset Options
                </button>
            </div>

            <!-- General Settings Section -->
            <div id="general-settings" class="settings-section active">
                <div class="settings-card">
                    <h3><span class="card-icon">💰</span>Currency Settings</h3>
                    <div class="setting-group">
                        <label for="currency-select">Currency:</label>
                        <select id="currency-select" class="setting-select">
                            <option value="USD">USD ($) - US Dollar</option>
                            <option value="EUR">EUR (€) - Euro</option>
                            <option value="GBP">GBP (£) - British Pound</option>
                            <option value="ZAR">ZAR (R) - South African Rand</option>
                            <option value="AUD">AUD (A$) - Australian Dollar</option>
                            <option value="CUSTOM">Custom Currency</option>
                        </select>
                    </div>
                    <div id="custom-currency-group" class="setting-group" style="display: none;">
                        <label for="custom-currency-name">Custom Currency Name:</label>
                        <input type="text" id="custom-currency-name" placeholder="e.g., INR" maxlength="10">
                        <label for="custom-currency-symbol">Currency Symbol:</label>
                        <input type="text" id="custom-currency-symbol" placeholder="e.g., ₹" maxlength="5">
                    </div>
                </div>

                <div class="settings-card">
                    <h3><span class="card-icon">⚡</span>Unit Settings</h3>
                    <div class="setting-group">
                        <label for="unit-select">Unit Type:</label>
                        <select id="unit-select" class="setting-select">
                            <option value="Units">Units (Generic electricity units)</option>
                            <option value="KWh">KWh (Kilowatt hours)</option>
                            <option value="CUSTOM">Custom Unit Type</option>
                        </select>
                    </div>
                    <div id="custom-unit-group" class="setting-group" style="display: none;">
                        <label for="custom-unit-name">Custom Unit Name:</label>
                        <input type="text" id="custom-unit-name" placeholder="e.g., Credits" maxlength="15">
                    </div>
                </div>

                <div class="settings-card">
                    <h3><span class="card-icon">💵</span>Cost Settings</h3>
                    <div class="setting-group">
                        <label for="cost-per-unit">Cost per Unit:</label>
                        <div class="input-with-preview">
                            <input type="number" id="cost-per-unit" step="0.001" min="0" placeholder="0.150">
                            <span class="input-preview" id="cost-preview">$0.150</span>
                        </div>
                        <small class="setting-help">This will be used for cost calculations</small>
                    </div>
                </div>

                <div class="settings-card">
                    <h3><span class="card-icon">⚠️</span>Alert Settings</h3>
                    <div class="setting-group">
                        <label for="low-threshold">Low Units Threshold:</label>
                        <div class="input-with-preview">
                            <input type="number" id="low-threshold" step="0.1" min="0" placeholder="10.0">
                            <span class="input-preview" id="threshold-preview">10.0 Units</span>
                        </div>
                        <small class="setting-help">Warning will show when units drop below this level</small>
                    </div>
                </div>

                <div class="settings-card">
                    <h3><span class="card-icon">🔔</span>Notification Settings</h3>
                    <div class="setting-group">
                        <label class="toggle-label">
                            <input type="checkbox" id="notifications-enabled" class="toggle-input">
                            <span class="toggle-slider"></span>
                            Enable Daily Reminders
                        </label>
                    </div>
                    <div id="notification-details" class="setting-group">
                        <label for="notification-time">Reminder Time:</label>
                        <input type="time" id="notification-time" class="setting-input">
                        <label for="notification-message">Custom Message:</label>
                        <textarea id="notification-message" class="setting-textarea" rows="2" placeholder="Don't forget to record your electricity usage!"></textarea>
                    </div>
                </div>
            </div>

            <!-- Appearance Settings Section -->
            <div id="appearance-settings" class="settings-section">
                <div class="settings-card">
                    <h3><span class="card-icon">🎨</span>Theme Selection</h3>
                    <div class="theme-grid">
                        <div class="theme-option" data-theme="electric-blue">
                            <div class="theme-preview electric-blue"></div>
                            <span class="theme-name">Electric Blue</span>
                        </div>
                        <div class="theme-option" data-theme="emerald-green">
                            <div class="theme-preview emerald-green"></div>
                            <span class="theme-name">Emerald Green</span>
                        </div>
                        <div class="theme-option" data-theme="sunset-orange">
                            <div class="theme-preview sunset-orange"></div>
                            <span class="theme-name">Sunset Orange</span>
                        </div>
                        <div class="theme-option" data-theme="royal-purple">
                            <div class="theme-preview royal-purple"></div>
                            <span class="theme-name">Royal Purple</span>
                        </div>
                        <div class="theme-option" data-theme="midnight-dark">
                            <div class="theme-preview midnight-dark"></div>
                            <span class="theme-name">Midnight Dark</span>
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3><span class="card-icon">🔤</span>Font Settings</h3>
                    <div class="setting-group">
                        <label for="font-size">Font Size:</label>
                        <select id="font-size" class="setting-select">
                            <option value="small">Small</option>
                            <option value="medium">Medium</option>
                            <option value="large">Large</option>
                        </select>
                    </div>
                    <div class="font-preview">
                        <p id="font-preview-text">This is how your text will look with the selected font size.</p>
                    </div>
                </div>

                <div class="settings-card">
                    <h3><span class="card-icon">🌈</span>Color Scheme</h3>
                    <div class="setting-group">
                        <label for="color-scheme">Color Scheme:</label>
                        <select id="color-scheme" class="setting-select">
                            <option value="default">Default</option>
                            <option value="high-contrast">High Contrast</option>
                            <option value="colorful">Colorful</option>
                            <option value="minimal">Minimal</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Reset Options Section -->
            <div id="reset-settings" class="settings-section">
                <div class="settings-card danger-card">
                    <h3><span class="card-icon">🔄</span>Dashboard Reset</h3>
                    <p class="reset-description">
                        Clear dashboard data including current units and usage records. 
                        Purchase history will be preserved.
                    </p>
                    <button id="dashboard-reset-btn" class="btn-warning">
                        <span class="btn-icon">🔄</span>
                        Reset Dashboard Data
                    </button>
                </div>

                <div class="settings-card danger-card">
                    <h3><span class="card-icon">⚠️</span>Factory Reset</h3>
                    <p class="reset-description">
                        Clear all data including purchases, usage records, and settings. 
                        This action cannot be undone.
                    </p>
                    <button id="factory-reset-btn" class="btn-danger">
                        <span class="btn-icon">⚠️</span>
                        Factory Reset
                    </button>
                </div>

                <div class="settings-card">
                    <h3><span class="card-icon">📤</span>Data Management</h3>
                    <p class="reset-description">
                        Export your data for backup or import previously exported data.
                    </p>
                    <div class="data-actions">
                        <button id="export-data-btn" class="btn-secondary">
                            <span class="btn-icon">📤</span>
                            Export Data
                        </button>
                        <label for="import-data-file" class="btn-secondary file-input-label">
                            <span class="btn-icon">📥</span>
                            Import Data
                            <input type="file" id="import-data-file" accept=".json" style="display: none;">
                        </label>
                    </div>
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        // Settings navigation
        document.querySelectorAll('.settings-nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const section = btn.dataset.section;
                this.showSection(section);
            });
        });

        // Currency settings
        const currencySelect = document.getElementById('currency-select');
        const customCurrencyGroup = document.getElementById('custom-currency-group');
        const customCurrencyName = document.getElementById('custom-currency-name');
        const customCurrencySymbol = document.getElementById('custom-currency-symbol');

        currencySelect?.addEventListener('change', (e) => {
            if (e.target.value === 'CUSTOM') {
                customCurrencyGroup.style.display = 'block';
            } else {
                customCurrencyGroup.style.display = 'none';
                this.updateCurrency(e.target.value);
            }
        });

        customCurrencyName?.addEventListener('input', () => this.updateCustomCurrency());
        customCurrencySymbol?.addEventListener('input', () => this.updateCustomCurrency());

        // Unit settings
        const unitSelect = document.getElementById('unit-select');
        const customUnitGroup = document.getElementById('custom-unit-group');
        const customUnitName = document.getElementById('custom-unit-name');

        unitSelect?.addEventListener('change', (e) => {
            if (e.target.value === 'CUSTOM') {
                customUnitGroup.style.display = 'block';
            } else {
                customUnitGroup.style.display = 'none';
                this.updateUnit(e.target.value);
            }
        });

        customUnitName?.addEventListener('input', () => this.updateCustomUnit());

        // Cost and threshold settings
        const costPerUnit = document.getElementById('cost-per-unit');
        const lowThreshold = document.getElementById('low-threshold');

        costPerUnit?.addEventListener('input', () => this.updateCostPerUnit());
        lowThreshold?.addEventListener('input', () => this.updateLowThreshold());

        // Notification settings
        const notificationsEnabled = document.getElementById('notifications-enabled');
        const notificationTime = document.getElementById('notification-time');
        const notificationMessage = document.getElementById('notification-message');

        notificationsEnabled?.addEventListener('change', () => this.updateNotificationSettings());
        notificationTime?.addEventListener('change', () => this.updateNotificationSettings());
        notificationMessage?.addEventListener('input', () => this.updateNotificationSettings());

        // Theme settings
        document.querySelectorAll('.theme-option').forEach(option => {
            option.addEventListener('click', (e) => {
                const theme = option.dataset.theme;
                this.updateTheme(theme);
            });
        });

        // Font and color settings
        const fontSize = document.getElementById('font-size');
        const colorScheme = document.getElementById('color-scheme');

        fontSize?.addEventListener('change', () => this.updateFontSize());
        colorScheme?.addEventListener('change', () => this.updateColorScheme());

        // Reset buttons
        const dashboardResetBtn = document.getElementById('dashboard-reset-btn');
        const factoryResetBtn = document.getElementById('factory-reset-btn');
        const exportDataBtn = document.getElementById('export-data-btn');
        const importDataFile = document.getElementById('import-data-file');

        dashboardResetBtn?.addEventListener('click', () => this.dashboardReset());
        factoryResetBtn?.addEventListener('click', () => this.factoryReset());
        exportDataBtn?.addEventListener('click', () => this.exportData());
        importDataFile?.addEventListener('change', (e) => this.importData(e));
    }

    showSection(sectionName) {
        // Update navigation
        document.querySelectorAll('.settings-nav-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.section === sectionName) {
                btn.classList.add('active');
            }
        });

        // Update sections
        document.querySelectorAll('.settings-section').forEach(section => {
            section.classList.remove('active');
        });

        const targetSection = document.getElementById(`${sectionName}-settings`);
        if (targetSection) {
            targetSection.classList.add('active');
        }

        this.currentSection = sectionName;
    }

    refresh() {
        this.loadCurrentSettings();
        this.updatePreviews();
    }

    loadCurrentSettings() {
        const settings = StorageManager.getSettings();
        
        // Currency settings
        const currencySelect = document.getElementById('currency-select');
        if (currencySelect) {
            if (settings.currency.isCustom) {
                currencySelect.value = 'CUSTOM';
                document.getElementById('custom-currency-group').style.display = 'block';
                document.getElementById('custom-currency-name').value = settings.currency.customName || settings.currency.name;
                document.getElementById('custom-currency-symbol').value = settings.currency.customSymbol || settings.currency.symbol;
            } else {
                currencySelect.value = settings.currency.name;
            }
        }

        // Unit settings
        const unitSelect = document.getElementById('unit-select');
        if (unitSelect) {
            if (settings.unit.isCustom) {
                unitSelect.value = 'CUSTOM';
                document.getElementById('custom-unit-group').style.display = 'block';
                document.getElementById('custom-unit-name').value = settings.unit.customName || settings.unit.name;
            } else {
                unitSelect.value = settings.unit.name;
            }
        }

        // Cost and threshold
        const costPerUnit = document.getElementById('cost-per-unit');
        const lowThreshold = document.getElementById('low-threshold');
        
        if (costPerUnit) costPerUnit.value = settings.costPerUnit;
        if (lowThreshold) lowThreshold.value = settings.lowUnitsThreshold;

        // Notifications
        const notificationsEnabled = document.getElementById('notifications-enabled');
        const notificationTime = document.getElementById('notification-time');
        const notificationMessage = document.getElementById('notification-message');

        if (notificationsEnabled) notificationsEnabled.checked = settings.notifications.enabled;
        if (notificationTime) notificationTime.value = settings.notifications.time;
        if (notificationMessage) notificationMessage.value = settings.notifications.message;

        // Appearance
        const fontSize = document.getElementById('font-size');
        const colorScheme = document.getElementById('color-scheme');

        if (fontSize) fontSize.value = settings.fontSize;
        if (colorScheme) colorScheme.value = settings.colorScheme;

        // Theme selection
        document.querySelectorAll('.theme-option').forEach(option => {
            option.classList.remove('active');
            if (option.dataset.theme === settings.theme) {
                option.classList.add('active');
            }
        });
    }

    updatePreviews() {
        const settings = StorageManager.getSettings();
        
        // Cost preview
        const costPreview = document.getElementById('cost-preview');
        if (costPreview) {
            costPreview.textContent = `${settings.currency.symbol}${settings.costPerUnit.toFixed(3)}`;
        }

        // Threshold preview
        const thresholdPreview = document.getElementById('threshold-preview');
        if (thresholdPreview) {
            thresholdPreview.textContent = `${settings.lowUnitsThreshold} ${settings.unit.name}`;
        }

        // Font preview
        this.updateFontPreview();
    }

    updateCurrency(currencyName) {
        const currencyMap = {
            'USD': { name: 'USD', symbol: '$' },
            'EUR': { name: 'EUR', symbol: '€' },
            'GBP': { name: 'GBP', symbol: '£' },
            'ZAR': { name: 'ZAR', symbol: 'R' },
            'AUD': { name: 'AUD', symbol: 'A$' }
        };

        const currency = currencyMap[currencyName];
        if (currency) {
            StorageManager.updateSetting('currency', { ...currency, isCustom: false });
            this.updatePreviews();
            this.refreshOtherManagers();
        }
    }

    updateCustomCurrency() {
        const name = document.getElementById('custom-currency-name')?.value || '';
        const symbol = document.getElementById('custom-currency-symbol')?.value || '';

        if (name && symbol) {
            StorageManager.updateSetting('currency', { name, symbol, isCustom: true });
            this.updatePreviews();
            this.refreshOtherManagers();
        }
    }

    updateUnit(unitName) {
        StorageManager.updateSetting('unit', { name: unitName, isCustom: false });
        this.updatePreviews();
        this.refreshOtherManagers();
    }

    updateCustomUnit() {
        const name = document.getElementById('custom-unit-name')?.value || '';

        if (name) {
            StorageManager.updateSetting('unit', { name, isCustom: true });
            this.updatePreviews();
            this.refreshOtherManagers();
        }
    }

    updateCostPerUnit() {
        const value = parseFloat(document.getElementById('cost-per-unit')?.value || 0);
        if (value > 0) {
            StorageManager.updateSetting('costPerUnit', value);
            this.updatePreviews();
            this.refreshOtherManagers();
        }
    }

    updateLowThreshold() {
        const value = parseFloat(document.getElementById('low-threshold')?.value || 0);
        if (value >= 0) {
            StorageManager.updateSetting('lowUnitsThreshold', value);
            this.updatePreviews();
            this.refreshOtherManagers();
        }
    }

    updateNotificationSettings() {
        const enabled = document.getElementById('notifications-enabled')?.checked || false;
        const time = document.getElementById('notification-time')?.value || '18:00';
        const message = document.getElementById('notification-message')?.value || 'Don\'t forget to record your electricity usage!';

        StorageManager.updateSetting('notifications', { enabled, time, message });

        if (window.notificationManager) {
            window.notificationManager.updateSettings();
        }
    }

    updateTheme(themeName) {
        StorageManager.updateSetting('theme', themeName);
        
        // Update theme selection UI
        document.querySelectorAll('.theme-option').forEach(option => {
            option.classList.remove('active');
            if (option.dataset.theme === themeName) {
                option.classList.add('active');
            }
        });

        if (window.themeManager) {
            window.themeManager.applyTheme(themeName);
        }
    }

    updateFontSize() {
        const fontSize = document.getElementById('font-size')?.value || 'medium';
        StorageManager.updateSetting('fontSize', fontSize);
        this.updateFontPreview();
        this.applyFontSize(fontSize);
    }

    updateColorScheme() {
        const colorScheme = document.getElementById('color-scheme')?.value || 'default';
        StorageManager.updateSetting('colorScheme', colorScheme);
        this.applyColorScheme(colorScheme);
    }

    updateFontPreview() {
        const fontSize = document.getElementById('font-size')?.value || 'medium';
        const preview = document.getElementById('font-preview-text');
        
        if (preview) {
            preview.className = `font-preview-${fontSize}`;
        }
    }

    applyFontSize(size) {
        const root = document.documentElement;
        const sizeMap = {
            'small': { base: '14px', lg: '16px', xl: '20px', xxl: '28px' },
            'medium': { base: '16px', lg: '18px', xl: '24px', xxl: '32px' },
            'large': { base: '18px', lg: '20px', xl: '28px', xxl: '36px' }
        };

        const sizes = sizeMap[size] || sizeMap.medium;
        root.style.setProperty('--font-size-md', sizes.base);
        root.style.setProperty('--font-size-lg', sizes.lg);
        root.style.setProperty('--font-size-xl', sizes.xl);
        root.style.setProperty('--font-size-xxl', sizes.xxl);
    }

    applyColorScheme(scheme) {
        document.body.className = document.body.className.replace(/color-scheme-\w+/g, '');
        document.body.classList.add(`color-scheme-${scheme}`);
    }

    dashboardReset() {
        if (confirm('⚠️ Dashboard Data Reset\n\nThis will clear:\n• Current units value\n• Usage records and history\n• Dashboard statistics\n\nPurchase history and settings will be preserved.\n\nContinue?')) {
            try {
                // Clear dashboard-specific data only
                StorageManager.setCurrentUnits(0);
                StorageManager.clearUsageRecords();

                // Keep purchases and settings intact
                console.log('Dashboard reset: Cleared units and usage records, preserved purchases and settings');

                this.showMessage('✅ Dashboard Reset Complete! Dashboard data cleared, purchases and settings preserved.', 'success');
                this.refreshOtherManagers();

                // Navigate to dashboard to show the reset state
                if (window.electricityApp) {
                    window.electricityApp.showPage('dashboard');
                }

            } catch (error) {
                console.error('Dashboard reset error:', error);
                this.showMessage('❌ Dashboard reset failed. Please try again.', 'error');
            }
        }
    }

    factoryReset() {
        if (confirm('⚠️ FACTORY RESET WARNING\n\nThis will permanently delete:\n• ALL purchase history\n• ALL usage records\n• Current units value\n• ALL settings and preferences\n• ALL app data\n\nThis action CANNOT be undone!\n\nContinue?')) {
            if (confirm('🚨 FINAL WARNING 🚨\n\nYou are about to delete ALL data permanently.\nThis includes everything - purchases, usage, settings.\n\nAfter reset, you will need to enter initial values to use the app.\n\nProceed with factory reset?')) {
                const confirmation = prompt('Type "DELETE" to confirm factory reset:');
                if (confirmation === 'DELETE') {
                    try {
                        // Clear absolutely everything
                        StorageManager.clearAllData();

                        console.log('Factory reset: All data cleared');

                        this.showMessage('✅ Factory Reset Complete! All data deleted. App will reload with default settings.', 'success');

                        // Force reload to start fresh
                        setTimeout(() => {
                            window.location.reload();
                        }, 3000);

                    } catch (error) {
                        console.error('Factory reset error:', error);
                        this.showMessage('❌ Factory reset failed. Please try again.', 'error');
                    }
                } else {
                    this.showMessage('Factory reset cancelled - confirmation text did not match.', 'info');
                }
            }
        }
    }

    exportData() {
        const data = StorageManager.exportData();
        if (!data) {
            this.showMessage('Failed to export data.', 'error');
            return;
        }

        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `electricity-app-backup-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showMessage('Data exported successfully!', 'success');
    }

    importData(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = e.target.result;
                if (StorageManager.importData(data)) {
                    this.showMessage('Data imported successfully! The app will reload.', 'success');
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    this.showMessage('Failed to import data. Please check the file format.', 'error');
                }
            } catch (error) {
                this.showMessage('Invalid file format.', 'error');
            }
        };
        reader.readAsText(file);
    }

    refreshOtherManagers() {
        // Refresh other managers when settings change
        if (window.dashboardManager) window.dashboardManager.refresh();
        if (window.purchasesManager) window.purchasesManager.refresh();
        if (window.usageManager) window.usageManager.refresh();
        if (window.historyManager) window.historyManager.refresh();
    }

    showMessage(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
        if (window.electricityApp) {
            window.electricityApp.showNotification(message, type);
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SettingsManager;
}
