<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>App Functionality Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .data-display {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔌 Electricity App - Functionality Test</h1>
    
    <div class="test-section">
        <h2>📊 Storage System Test</h2>
        <button onclick="testStorage()">Test Storage</button>
        <button onclick="clearStorage()">Clear Storage</button>
        <div id="storage-results"></div>
    </div>

    <div class="test-section">
        <h2>💰 Purchase System Test</h2>
        <button onclick="testPurchases()">Add Test Purchase</button>
        <button onclick="showPurchases()">Show Purchases</button>
        <div id="purchase-results"></div>
    </div>

    <div class="test-section">
        <h2>⚡ Usage System Test</h2>
        <button onclick="testUsage()">Add Test Usage</button>
        <button onclick="showUsage()">Show Usage Records</button>
        <div id="usage-results"></div>
    </div>

    <div class="test-section">
        <h2>📈 Analytics Test</h2>
        <button onclick="testAnalytics()">Show Analytics</button>
        <div id="analytics-results"></div>
    </div>

    <div class="test-section">
        <h2>🎨 Theme System Test</h2>
        <button onclick="testThemes()">Test Themes</button>
        <div id="theme-results"></div>
    </div>

    <script src="js/storage.js"></script>
    <script src="js/themes.js"></script>
    <script>
        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        async function testStorage() {
            clearResults('storage-results');
            
            try {
                // Initialize storage
                const initResult = await StorageManager.init();
                showResult('storage-results', `✅ Storage initialization: ${initResult ? 'SUCCESS' : 'FAILED'}`, initResult ? 'success' : 'error');
                
                // Test settings
                const settings = StorageManager.getSettings();
                showResult('storage-results', `📋 Settings loaded: ${settings ? 'SUCCESS' : 'FAILED'}`, settings ? 'success' : 'error');
                
                // Test current units
                StorageManager.setCurrentUnits(50);
                const units = StorageManager.getCurrentUnits();
                showResult('storage-results', `⚡ Units test: Set 50, Got ${units} - ${units === 50 ? 'SUCCESS' : 'FAILED'}`, units === 50 ? 'success' : 'error');
                
                // Show current data size
                const dataSize = StorageManager.getDataSize();
                showResult('storage-results', `💾 Data size: ${dataSize} characters`, 'info');
                
            } catch (error) {
                showResult('storage-results', `❌ Storage test failed: ${error.message}`, 'error');
            }
        }

        function testPurchases() {
            clearResults('purchase-results');
            
            try {
                const testPurchase = {
                    currency: '$',
                    amount: 25.00,
                    units: 100,
                    costPerUnit: 0.25,
                    previousUnits: 50,
                    newTotal: 150
                };
                
                const result = StorageManager.addPurchase(testPurchase);
                showResult('purchase-results', `💰 Purchase added: ${result ? 'SUCCESS' : 'FAILED'}`, result ? 'success' : 'error');
                
                if (result) {
                    showResult('purchase-results', `📝 Purchase ID: ${result.id}`, 'info');
                    showResult('purchase-results', `💵 Amount: ${result.currency}${result.amount}`, 'info');
                    showResult('purchase-results', `⚡ Units: ${result.units}`, 'info');
                }
                
            } catch (error) {
                showResult('purchase-results', `❌ Purchase test failed: ${error.message}`, 'error');
            }
        }

        function showPurchases() {
            clearResults('purchase-results');
            
            try {
                const purchases = StorageManager.getPurchases();
                showResult('purchase-results', `📊 Total purchases: ${purchases.length}`, 'info');
                
                if (purchases.length > 0) {
                    const display = purchases.map(p => 
                        `ID: ${p.id}\nDate: ${new Date(p.timestamp).toLocaleString()}\nAmount: ${p.currency}${p.amount}\nUnits: ${p.units}`
                    ).join('\n\n');
                    
                    const dataDiv = document.createElement('div');
                    dataDiv.className = 'data-display';
                    dataDiv.textContent = display;
                    document.getElementById('purchase-results').appendChild(dataDiv);
                }
                
            } catch (error) {
                showResult('purchase-results', `❌ Show purchases failed: ${error.message}`, 'error');
            }
        }

        function testUsage() {
            clearResults('usage-results');
            
            try {
                const testUsage = {
                    previousUnits: 150,
                    currentUnits: 120,
                    usage: 30
                };
                
                const result = StorageManager.addUsageRecord(testUsage);
                showResult('usage-results', `⚡ Usage record added: ${result ? 'SUCCESS' : 'FAILED'}`, result ? 'success' : 'error');
                
                if (result) {
                    showResult('usage-results', `📝 Record ID: ${result.id}`, 'info');
                    showResult('usage-results', `⚡ Usage: ${result.usage} units`, 'info');
                }
                
            } catch (error) {
                showResult('usage-results', `❌ Usage test failed: ${error.message}`, 'error');
            }
        }

        function showUsage() {
            clearResults('usage-results');
            
            try {
                const records = StorageManager.getUsageRecords();
                showResult('usage-results', `📊 Total usage records: ${records.length}`, 'info');
                
                if (records.length > 0) {
                    const display = records.map(r => 
                        `ID: ${r.id}\nDate: ${new Date(r.timestamp).toLocaleString()}\nUsage: ${r.usage} units\nFrom: ${r.previousUnits} → ${r.currentUnits}`
                    ).join('\n\n');
                    
                    const dataDiv = document.createElement('div');
                    dataDiv.className = 'data-display';
                    dataDiv.textContent = display;
                    document.getElementById('usage-results').appendChild(dataDiv);
                }
                
            } catch (error) {
                showResult('usage-results', `❌ Show usage failed: ${error.message}`, 'error');
            }
        }

        function testAnalytics() {
            clearResults('analytics-results');
            
            try {
                const analytics = StorageManager.getUsageAnalytics();
                showResult('analytics-results', `📈 Analytics generated: SUCCESS`, 'success');
                
                const display = `Weekly Usage: ${analytics.weekly.usage} units (${analytics.weekly.records} records)
Weekly Cost: $${analytics.weekly.cost.toFixed(2)}

Monthly Usage: ${analytics.monthly.usage} units (${analytics.monthly.records} records)
Monthly Cost: $${analytics.monthly.cost.toFixed(2)}

Total Usage: ${analytics.total.usage} units (${analytics.total.records} records)`;
                
                const dataDiv = document.createElement('div');
                dataDiv.className = 'data-display';
                dataDiv.textContent = display;
                document.getElementById('analytics-results').appendChild(dataDiv);
                
            } catch (error) {
                showResult('analytics-results', `❌ Analytics test failed: ${error.message}`, 'error');
            }
        }

        function testThemes() {
            clearResults('theme-results');
            
            try {
                if (typeof ThemeManager !== 'undefined') {
                    showResult('theme-results', `🎨 ThemeManager available: SUCCESS`, 'success');
                    
                    const themes = ['electric-blue', 'sunset-orange', 'forest-green', 'royal-purple', 'midnight-dark'];
                    showResult('theme-results', `🎨 Available themes: ${themes.join(', ')}`, 'info');
                } else {
                    showResult('theme-results', `❌ ThemeManager not available`, 'error');
                }
                
            } catch (error) {
                showResult('theme-results', `❌ Theme test failed: ${error.message}`, 'error');
            }
        }

        function clearStorage() {
            if (confirm('Are you sure you want to clear all storage data?')) {
                StorageManager.factoryReset();
                showResult('storage-results', `🗑️ Storage cleared and reset to defaults`, 'info');
            }
        }

        // Auto-run basic tests on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testStorage();
            }, 1000);
        });
    </script>
</body>
</html>
