<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Content Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .element-list {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .element-found {
            color: #28a745;
        }
        .element-missing {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <h1>🔌 Page Content Test</h1>
    
    <div class="test-section">
        <h2>📄 HTML Elements Check</h2>
        <button onclick="checkElements()">Check All Elements</button>
        <div id="elements-results"></div>
    </div>

    <div class="test-section">
        <h2>📊 Page Navigation Test</h2>
        <button onclick="testNavigation()">Test Page Switching</button>
        <div id="navigation-results"></div>
    </div>

    <div class="test-section">
        <h2>💰 Purchases Page Test</h2>
        <button onclick="testPurchasesPage()">Test Purchases</button>
        <div id="purchases-results"></div>
    </div>

    <div class="test-section">
        <h2>📈 Usage Page Test</h2>
        <button onclick="testUsagePage()">Test Usage</button>
        <div id="usage-results"></div>
    </div>

    <script>
        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        function checkElements() {
            clearResults('elements-results');
            
            const elementsToCheck = [
                // Main app elements
                'app',
                'sidebar',
                'main-content',
                
                // Dashboard elements
                'dashboard-page',
                'current-units',
                'usage-dial',
                
                // Purchases elements
                'purchases-page',
                'purchase-form',
                'purchase-amount',
                'purchase-units',
                'purchase-currency-symbol',
                'purchase-unit-label',
                'preview-cost-per-unit',
                'preview-current-units',
                'preview-new-total',
                'purchases-list',
                
                // Usage elements
                'usage-page',
                'usage-form',
                'new-units',
                'current-units-display',
                'usage-chart',
                'usage-list',
                
                // History elements
                'history-page',
                'weekly-usage',
                'monthly-usage',
                'total-purchases',
                'history-list',
                
                // Settings elements
                'settings-page',
                'currency-select',
                'unit-select',
                'cost-per-unit',
                'low-units-threshold',
                'theme-select',
                'notifications-enabled'
            ];
            
            let found = 0;
            let missing = 0;
            let results = '';
            
            elementsToCheck.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    results += `<div class="element-found">✅ ${id}</div>`;
                    found++;
                } else {
                    results += `<div class="element-missing">❌ ${id}</div>`;
                    missing++;
                }
            });
            
            showResult('elements-results', `<strong>Elements Check:</strong> ${found} found, ${missing} missing`, found > missing ? 'success' : 'error');
            
            const elementsList = document.createElement('div');
            elementsList.className = 'element-list';
            elementsList.innerHTML = results;
            document.getElementById('elements-results').appendChild(elementsList);
        }

        function testNavigation() {
            clearResults('navigation-results');
            
            const pages = ['dashboard', 'purchases', 'usage', 'history', 'settings'];
            let working = 0;
            
            pages.forEach(page => {
                const pageElement = document.getElementById(`${page}-page`);
                if (pageElement) {
                    showResult('navigation-results', `✅ ${page} page exists`, 'success');
                    working++;
                } else {
                    showResult('navigation-results', `❌ ${page} page missing`, 'error');
                }
            });
            
            showResult('navigation-results', `<strong>Navigation Test:</strong> ${working}/${pages.length} pages found`, working === pages.length ? 'success' : 'error');
        }

        function testPurchasesPage() {
            clearResults('purchases-results');
            
            // Check if we can access the main app
            if (typeof window.electricityApp === 'undefined') {
                showResult('purchases-results', '❌ Main app not loaded', 'error');
                return;
            }
            
            // Check purchases manager
            if (typeof window.purchasesManager === 'undefined') {
                showResult('purchases-results', '❌ PurchasesManager not loaded', 'error');
                return;
            }
            
            showResult('purchases-results', '✅ PurchasesManager loaded', 'success');
            
            // Check form elements
            const form = document.getElementById('purchase-form');
            const amountInput = document.getElementById('purchase-amount');
            const unitsInput = document.getElementById('purchase-units');
            
            if (form && amountInput && unitsInput) {
                showResult('purchases-results', '✅ Purchase form elements found', 'success');
                
                // Test form interaction
                try {
                    amountInput.value = '25.00';
                    unitsInput.value = '100';
                    
                    // Trigger calculation
                    amountInput.dispatchEvent(new Event('input'));
                    
                    showResult('purchases-results', '✅ Form interaction test passed', 'success');
                } catch (error) {
                    showResult('purchases-results', `❌ Form interaction failed: ${error.message}`, 'error');
                }
            } else {
                showResult('purchases-results', '❌ Purchase form elements missing', 'error');
            }
        }

        function testUsagePage() {
            clearResults('usage-results');
            
            // Check usage manager
            if (typeof window.usageManager === 'undefined') {
                showResult('usage-results', '❌ UsageManager not loaded', 'error');
                return;
            }
            
            showResult('usage-results', '✅ UsageManager loaded', 'success');
            
            // Check form elements
            const form = document.getElementById('usage-form');
            const newUnitsInput = document.getElementById('new-units');
            const currentUnitsDisplay = document.getElementById('current-units-display');
            
            if (form && newUnitsInput && currentUnitsDisplay) {
                showResult('usage-results', '✅ Usage form elements found', 'success');
            } else {
                showResult('usage-results', '❌ Usage form elements missing', 'error');
            }
            
            // Check chart
            const chart = document.getElementById('usage-chart');
            if (chart) {
                showResult('usage-results', '✅ Usage chart element found', 'success');
            } else {
                showResult('usage-results', '❌ Usage chart element missing', 'error');
            }
        }

        // Auto-run tests on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkElements();
            }, 1000);
        });
    </script>
</body>
</html>
