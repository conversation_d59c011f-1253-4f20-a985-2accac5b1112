// Dashboard Manager - Handles the main dashboard functionality
class DashboardManager {
    constructor() {
        this.dialCanvas = null;
        this.dialContext = null;
        this.animationFrame = null;
        this.currentDialValue = 0;
        this.targetDialValue = 0;
        this.init();
    }

    init() {
        this.setupDial();
        this.refresh();
        
        // Auto-refresh every 30 seconds
        setInterval(() => this.refresh(), 30000);
    }

    setupDial() {
        try {
            this.dialCanvas = document.getElementById('usage-dial');
            if (!this.dialCanvas) {
                console.warn('Usage dial canvas not found');
                return;
            }

            this.dialContext = this.dialCanvas.getContext('2d');
            if (!this.dialContext) {
                console.warn('Canvas 2D context not available');
                return;
            }

            // Set canvas size for high DPI displays
            const rect = this.dialCanvas.getBoundingClientRect();
            const dpr = window.devicePixelRatio || 1;

            // Ensure minimum canvas size
            const width = Math.max(100, rect.width);
            const height = Math.max(100, rect.height);

            this.dialCanvas.width = width * dpr;
            this.dialCanvas.height = height * dpr;
            this.dialContext.scale(dpr, dpr);

            // Start dial animation
            this.animateDial();
        } catch (error) {
            console.error('Error setting up dial:', error);
        }
    }

    refresh() {
        this.updateCurrentUnits();
        this.updateUsageSinceLastRecording();
        this.updateUsageSummary();
        this.updateDialValue();
        this.checkLowUnitsWarning();
    }

    updateCurrentUnits() {
        const currentUnits = StorageManager.getCurrentUnits();
        const settings = StorageManager.getSettings();
        
        const currentUnitsElement = document.getElementById('current-units');
        const currentUnitsLabelElement = document.getElementById('current-units-label');
        
        if (currentUnitsElement) {
            currentUnitsElement.textContent = currentUnits.toFixed(1);
        }
        
        if (currentUnitsLabelElement) {
            currentUnitsLabelElement.textContent = settings.unit.name;
        }
    }

    updateUsageSinceLastRecording() {
        const lastRecord = StorageManager.getLastUsageRecord();
        const currentUnits = StorageManager.getCurrentUnits();
        const settings = StorageManager.getSettings();
        
        let usageSinceLastElement = document.getElementById('usage-since-last');
        let usageUnitsLabelElement = document.getElementById('usage-units-label');
        
        if (lastRecord && lastRecord.currentUnits !== undefined) {
            const usage = lastRecord.currentUnits - currentUnits;
            
            if (usageSinceLastElement) {
                usageSinceLastElement.textContent = Math.abs(usage).toFixed(1);
            }
        } else {
            if (usageSinceLastElement) {
                usageSinceLastElement.textContent = '0.0';
            }
        }
        
        if (usageUnitsLabelElement) {
            usageUnitsLabelElement.textContent = settings.unit.name;
        }
    }

    updateUsageSummary() {
        const analytics = StorageManager.getUsageAnalytics();
        const settings = StorageManager.getSettings();
        
        // Weekly usage
        const weeklyUnitsElement = document.getElementById('weekly-units');
        const weeklyUnitsLabelElement = document.getElementById('weekly-units-label');
        const weeklyCostElement = document.getElementById('weekly-cost');
        
        if (weeklyUnitsElement) {
            weeklyUnitsElement.textContent = analytics.weekly.usage.toFixed(1);
        }
        if (weeklyUnitsLabelElement) {
            weeklyUnitsLabelElement.textContent = settings.unit.name;
        }
        if (weeklyCostElement) {
            weeklyCostElement.textContent = `${settings.currency.symbol}${analytics.weekly.cost.toFixed(2)}`;
        }
        
        // Monthly usage
        const monthlyUnitsElement = document.getElementById('monthly-units');
        const monthlyUnitsLabelElement = document.getElementById('monthly-units-label');
        const monthlyCostElement = document.getElementById('monthly-cost');
        
        if (monthlyUnitsElement) {
            monthlyUnitsElement.textContent = analytics.monthly.usage.toFixed(1);
        }
        if (monthlyUnitsLabelElement) {
            monthlyUnitsLabelElement.textContent = settings.unit.name;
        }
        if (monthlyCostElement) {
            monthlyCostElement.textContent = `${settings.currency.symbol}${analytics.monthly.cost.toFixed(2)}`;
        }
    }

    updateDialValue() {
        const currentUnits = StorageManager.getCurrentUnits();
        const settings = StorageManager.getSettings();
        const threshold = settings.lowUnitsThreshold;
        
        // Calculate percentage based on threshold (threshold = 0%, threshold*3 = 100%)
        const maxValue = threshold * 3;
        let percentage = Math.min((currentUnits / maxValue) * 100, 100);
        
        // If units are below threshold, show as red zone
        if (currentUnits <= threshold) {
            percentage = (currentUnits / threshold) * 20; // 0-20% for red zone
        } else {
            percentage = 20 + ((currentUnits - threshold) / (maxValue - threshold)) * 80; // 20-100% for normal zone
        }
        
        this.targetDialValue = Math.max(0, Math.min(100, percentage));
        
        // Update dial percentage display
        const dialPercentageElement = document.getElementById('dial-percentage');
        if (dialPercentageElement) {
            dialPercentageElement.textContent = `${Math.round(percentage)}%`;
        }
    }

    animateDial() {
        if (!this.dialContext) return;
        
        // Smooth animation towards target value
        const diff = this.targetDialValue - this.currentDialValue;
        this.currentDialValue += diff * 0.1;
        
        this.drawDial();
        
        this.animationFrame = requestAnimationFrame(() => this.animateDial());
    }

    drawDial() {
        try {
            if (!this.dialContext || !this.dialCanvas) return;

            const ctx = this.dialContext;
            const canvas = this.dialCanvas;
            const centerX = canvas.width / (window.devicePixelRatio || 1) / 2;
            const centerY = canvas.height / (window.devicePixelRatio || 1) / 2;
            const radius = Math.max(40, Math.min(centerX, centerY) - 40);

            // Clear canvas with transparent background
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Get current data for dynamic styling
            const currentUnits = StorageManager.getCurrentUnits();
            const settings = StorageManager.getSettings();
            const threshold = settings.lowUnitsThreshold;

            // Determine status and colors
            let statusColor, statusText, glowColor;
            if (currentUnits <= threshold) {
                statusColor = '#ff4757'; // Critical red
                statusText = 'LOW';
                glowColor = 'rgba(255, 71, 87, 0.3)';
            } else if (currentUnits <= threshold * 1.5) {
                statusColor = '#ffa502'; // Warning orange
                statusText = 'MEDIUM';
                glowColor = 'rgba(255, 165, 2, 0.3)';
            } else {
                statusColor = '#2ed573'; // Good green
                statusText = 'GOOD';
                glowColor = 'rgba(46, 213, 115, 0.3)';
            }

            // Create outer glow effect
            ctx.shadowColor = glowColor;
            ctx.shadowBlur = 25;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;

            // Draw outer ring (background track)
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.lineWidth = 16;
            ctx.stroke();

            // Reset shadow for main arc
            ctx.shadowBlur = 0;

            // Create dynamic gradient based on theme and status
            const theme = settings.theme || 'electric-blue';
            const gradientColors = this.getThemeGradientColors(theme, statusColor);

            const gradient = ctx.createLinearGradient(
                centerX - radius, centerY - radius,
                centerX + radius, centerY + radius
            );
            gradient.addColorStop(0, gradientColors[0]);
            gradient.addColorStop(0.5, gradientColors[1]);
            gradient.addColorStop(1, gradientColors[2]);

            // Draw main progress arc
            const startAngle = -Math.PI / 2; // Start at top
            const endAngle = startAngle + (this.currentDialValue / 100) * 2 * Math.PI;

            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, startAngle, endAngle);
            ctx.strokeStyle = gradient;
            ctx.lineWidth = 16;
            ctx.lineCap = 'round';
            ctx.stroke();

            // Draw inner highlight arc
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius - 8, startAngle, endAngle);
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.4)';
            ctx.lineWidth = 4;
            ctx.lineCap = 'round';
            ctx.stroke();

            // Draw modern tick marks
            this.drawModernTickMarks(ctx, centerX, centerY, radius);

            // Draw center circle with glass effect
            this.drawCenterCircle(ctx, centerX, centerY, statusColor);

            // Update text displays
            this.updateDialDisplays(currentUnits, this.currentDialValue, statusText, statusColor);

        } catch (error) {
            console.error('Error drawing modern dial:', error);
        }
    }

    getThemeGradientColors(theme, statusColor) {
        const themeColors = {
            'electric-blue': [statusColor, '#2196F3', '#1976D2'],
            'sunset-orange': [statusColor, '#FF9800', '#F57C00'],
            'forest-green': [statusColor, '#4CAF50', '#388E3C'],
            'royal-purple': [statusColor, '#9C27B0', '#7B1FA2'],
            'midnight-dark': [statusColor, '#37474F', '#263238']
        };
        return themeColors[theme] || themeColors['electric-blue'];
    }

    drawModernTickMarks(ctx, centerX, centerY, radius) {
        try {
            const majorTicks = 8;
            const minorTicks = 16;

            // Draw minor tick marks
            for (let i = 0; i < minorTicks; i++) {
                const angle = (i / minorTicks) * 2 * Math.PI - Math.PI / 2;
                const startRadius = radius + 8;
                const endRadius = radius + 12;

                const startX = centerX + Math.cos(angle) * startRadius;
                const startY = centerY + Math.sin(angle) * startRadius;
                const endX = centerX + Math.cos(angle) * endRadius;
                const endY = centerY + Math.sin(angle) * endRadius;

                ctx.beginPath();
                ctx.moveTo(startX, startY);
                ctx.lineTo(endX, endY);
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
                ctx.lineWidth = 1;
                ctx.stroke();
            }

            // Draw major tick marks
            for (let i = 0; i < majorTicks; i++) {
                const angle = (i / majorTicks) * 2 * Math.PI - Math.PI / 2;
                const startRadius = radius + 6;
                const endRadius = radius + 16;

                const startX = centerX + Math.cos(angle) * startRadius;
                const startY = centerY + Math.sin(angle) * startRadius;
                const endX = centerX + Math.cos(angle) * endRadius;
                const endY = centerY + Math.sin(angle) * endRadius;

                ctx.beginPath();
                ctx.moveTo(startX, startY);
                ctx.lineTo(endX, endY);
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.6)';
                ctx.lineWidth = 2;
                ctx.stroke();
            }
        } catch (error) {
            console.error('Error drawing modern tick marks:', error);
        }
    }

    drawCenterCircle(ctx, centerX, centerY, statusColor) {
        try {
            // Outer center circle with glass effect
            const centerGradient = ctx.createRadialGradient(
                centerX, centerY - 10, 0,
                centerX, centerY, 50
            );
            centerGradient.addColorStop(0, 'rgba(255, 255, 255, 0.9)');
            centerGradient.addColorStop(0.7, 'rgba(255, 255, 255, 0.3)');
            centerGradient.addColorStop(1, 'rgba(255, 255, 255, 0.1)');

            ctx.beginPath();
            ctx.arc(centerX, centerY, 50, 0, 2 * Math.PI);
            ctx.fillStyle = centerGradient;
            ctx.fill();

            // Inner ring with status color
            ctx.beginPath();
            ctx.arc(centerX, centerY, 45, 0, 2 * Math.PI);
            ctx.strokeStyle = statusColor;
            ctx.lineWidth = 2;
            ctx.stroke();

            // Inner highlight
            ctx.beginPath();
            ctx.arc(centerX, centerY - 15, 20, 0, Math.PI);
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.6)';
            ctx.lineWidth = 3;
            ctx.stroke();

        } catch (error) {
            console.error('Error drawing center circle:', error);
        }
    }

    updateDialDisplays(currentUnits, percentage, statusText, statusColor) {
        // Update percentage display
        const percentageElement = document.getElementById('dial-percentage');
        if (percentageElement) {
            percentageElement.textContent = `${Math.round(percentage)}%`;
            percentageElement.style.color = statusColor;
        }

        // Update status text
        const statusElement = document.getElementById('dial-status');
        if (statusElement) {
            statusElement.textContent = statusText;
            statusElement.style.color = statusColor;
        }

        // Update units display in center
        const unitsElement = document.getElementById('dial-units');
        if (unitsElement) {
            const settings = StorageManager.getSettings();
            const unitName = settings.unit.isCustom ? settings.unit.customName : settings.unit.name;
            unitsElement.textContent = `${currentUnits.toFixed(1)} ${unitName}`;
        }
    }

    checkLowUnitsWarning() {
        const currentUnits = StorageManager.getCurrentUnits();
        const settings = StorageManager.getSettings();
        const threshold = settings.lowUnitsThreshold;
        
        const warningElement = document.getElementById('low-units-warning');
        
        if (warningElement) {
            if (currentUnits <= threshold) {
                warningElement.style.display = 'flex';
                warningElement.classList.add('warning-active');
                
                // Show notification if enabled
                if (settings.notifications.enabled && window.notificationManager) {
                    window.notificationManager.showLowUnitsNotification(currentUnits, threshold);
                }
            } else {
                warningElement.style.display = 'none';
                warningElement.classList.remove('warning-active');
            }
        }
    }

    // Public methods for external use
    updateUnits(newUnits) {
        StorageManager.setCurrentUnits(newUnits);
        this.refresh();
    }

    addPurchase(currency, units) {
        const currentUnits = StorageManager.getCurrentUnits();
        const newTotal = currentUnits + units;
        
        // Add purchase record
        StorageManager.addPurchase({
            currency: currency,
            units: units,
            previousUnits: currentUnits,
            newTotal: newTotal
        });
        
        // Update current units
        StorageManager.setCurrentUnits(newTotal);
        this.refresh();
    }

    recordUsage(newUnits) {
        const currentUnits = StorageManager.getCurrentUnits();
        const usage = currentUnits - newUnits;
        
        if (usage > 0) {
            // Add usage record
            StorageManager.addUsageRecord({
                previousUnits: currentUnits,
                currentUnits: newUnits,
                usage: usage
            });
            
            // Update current units
            StorageManager.setCurrentUnits(newUnits);
            this.refresh();
        }
    }

    destroy() {
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DashboardManager;
}
