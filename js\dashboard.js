// Dashboard Manager - Handles the main dashboard functionality
class DashboardManager {
    constructor() {
        this.dialCanvas = null;
        this.dialContext = null;
        this.animationFrame = null;
        this.currentDialValue = 0;
        this.targetDialValue = 0;
        this.init();
    }

    init() {
        this.setupDial();
        this.refresh();
        
        // Auto-refresh every 30 seconds
        setInterval(() => this.refresh(), 30000);
    }

    setupDial() {
        try {
            this.dialCanvas = document.getElementById('usage-dial');
            if (!this.dialCanvas) {
                console.warn('Usage dial canvas not found');
                return;
            }

            this.dialContext = this.dialCanvas.getContext('2d');
            if (!this.dialContext) {
                console.warn('Canvas 2D context not available');
                return;
            }

            // Set canvas size for high DPI displays
            const rect = this.dialCanvas.getBoundingClientRect();
            const dpr = window.devicePixelRatio || 1;

            // Ensure minimum canvas size
            const width = Math.max(100, rect.width);
            const height = Math.max(100, rect.height);

            this.dialCanvas.width = width * dpr;
            this.dialCanvas.height = height * dpr;
            this.dialContext.scale(dpr, dpr);

            // Start dial animation
            this.animateDial();
        } catch (error) {
            console.error('Error setting up dial:', error);
        }
    }

    refresh() {
        this.updateCurrentUnits();
        this.updateUsageSinceLastRecording();
        this.updateUsageSummary();
        this.updateDialValue();
        this.checkLowUnitsWarning();
    }

    updateCurrentUnits() {
        const currentUnits = StorageManager.getCurrentUnits();
        const settings = StorageManager.getSettings();
        
        const currentUnitsElement = document.getElementById('current-units');
        const currentUnitsLabelElement = document.getElementById('current-units-label');
        
        if (currentUnitsElement) {
            currentUnitsElement.textContent = currentUnits.toFixed(1);
        }
        
        if (currentUnitsLabelElement) {
            currentUnitsLabelElement.textContent = settings.unit.name;
        }
    }

    updateUsageSinceLastRecording() {
        const lastRecord = StorageManager.getLastUsageRecord();
        const currentUnits = StorageManager.getCurrentUnits();
        const settings = StorageManager.getSettings();
        
        let usageSinceLastElement = document.getElementById('usage-since-last');
        let usageUnitsLabelElement = document.getElementById('usage-units-label');
        
        if (lastRecord && lastRecord.currentUnits !== undefined) {
            const usage = lastRecord.currentUnits - currentUnits;
            
            if (usageSinceLastElement) {
                usageSinceLastElement.textContent = Math.abs(usage).toFixed(1);
            }
        } else {
            if (usageSinceLastElement) {
                usageSinceLastElement.textContent = '0.0';
            }
        }
        
        if (usageUnitsLabelElement) {
            usageUnitsLabelElement.textContent = settings.unit.name;
        }
    }

    updateUsageSummary() {
        const analytics = StorageManager.getUsageAnalytics();
        const settings = StorageManager.getSettings();
        
        // Weekly usage
        const weeklyUnitsElement = document.getElementById('weekly-units');
        const weeklyUnitsLabelElement = document.getElementById('weekly-units-label');
        const weeklyCostElement = document.getElementById('weekly-cost');
        
        if (weeklyUnitsElement) {
            weeklyUnitsElement.textContent = analytics.weekly.usage.toFixed(1);
        }
        if (weeklyUnitsLabelElement) {
            weeklyUnitsLabelElement.textContent = settings.unit.name;
        }
        if (weeklyCostElement) {
            weeklyCostElement.textContent = `${settings.currency.symbol}${analytics.weekly.cost.toFixed(2)}`;
        }
        
        // Monthly usage
        const monthlyUnitsElement = document.getElementById('monthly-units');
        const monthlyUnitsLabelElement = document.getElementById('monthly-units-label');
        const monthlyCostElement = document.getElementById('monthly-cost');
        
        if (monthlyUnitsElement) {
            monthlyUnitsElement.textContent = analytics.monthly.usage.toFixed(1);
        }
        if (monthlyUnitsLabelElement) {
            monthlyUnitsLabelElement.textContent = settings.unit.name;
        }
        if (monthlyCostElement) {
            monthlyCostElement.textContent = `${settings.currency.symbol}${analytics.monthly.cost.toFixed(2)}`;
        }
    }

    updateDialValue() {
        const currentUnits = StorageManager.getCurrentUnits();
        const settings = StorageManager.getSettings();
        const threshold = settings.lowUnitsThreshold;
        
        // Calculate percentage based on threshold (threshold = 0%, threshold*3 = 100%)
        const maxValue = threshold * 3;
        let percentage = Math.min((currentUnits / maxValue) * 100, 100);
        
        // If units are below threshold, show as red zone
        if (currentUnits <= threshold) {
            percentage = (currentUnits / threshold) * 20; // 0-20% for red zone
        } else {
            percentage = 20 + ((currentUnits - threshold) / (maxValue - threshold)) * 80; // 20-100% for normal zone
        }
        
        this.targetDialValue = Math.max(0, Math.min(100, percentage));
        
        // Update dial percentage display
        const dialPercentageElement = document.getElementById('dial-percentage');
        if (dialPercentageElement) {
            dialPercentageElement.textContent = `${Math.round(percentage)}%`;
        }
    }

    animateDial() {
        if (!this.dialContext) return;
        
        // Smooth animation towards target value
        const diff = this.targetDialValue - this.currentDialValue;
        this.currentDialValue += diff * 0.1;
        
        this.drawDial();
        
        this.animationFrame = requestAnimationFrame(() => this.animateDial());
    }

    drawDial() {
        try {
            if (!this.dialContext || !this.dialCanvas) return;

            const ctx = this.dialContext;
            const canvas = this.dialCanvas;
            const centerX = canvas.width / (window.devicePixelRatio || 1) / 2;
            const centerY = canvas.height / (window.devicePixelRatio || 1) / 2;
            const radius = Math.max(10, Math.min(centerX, centerY) - 20);
        
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Draw background circle
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        ctx.strokeStyle = '#E0E0E0';
        ctx.lineWidth = 8;
        ctx.stroke();
        
        // Draw progress arc
        const startAngle = -Math.PI / 2; // Start at top
        const endAngle = startAngle + (this.currentDialValue / 100) * 2 * Math.PI;
        
        // Determine color based on value
        let gradient = ctx.createLinearGradient(centerX - radius, centerY - radius, centerX + radius, centerY + radius);
        
        if (this.currentDialValue <= 20) {
            // Red zone (low units)
            gradient.addColorStop(0, '#FF5252');
            gradient.addColorStop(1, '#F44336');
        } else if (this.currentDialValue <= 50) {
            // Orange zone (medium units)
            gradient.addColorStop(0, '#FF9800');
            gradient.addColorStop(1, '#F57C00');
        } else {
            // Green zone (good units)
            gradient.addColorStop(0, '#4CAF50');
            gradient.addColorStop(1, '#2E7D32');
        }
        
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, startAngle, endAngle);
        ctx.strokeStyle = gradient;
        ctx.lineWidth = 12;
        ctx.lineCap = 'round';
        ctx.stroke();
        
        // Draw inner glow effect
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius - 6, startAngle, endAngle);
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.lineWidth = 4;
        ctx.lineCap = 'round';
        ctx.stroke();
        
        // Draw tick marks
        this.drawTickMarks(ctx, centerX, centerY, radius);
        
        // Draw center dot
        ctx.beginPath();
        ctx.arc(centerX, centerY, 6, 0, 2 * Math.PI);
        ctx.fillStyle = '#333';
        ctx.fill();
        } catch (error) {
            console.error('Error drawing dial:', error);
        }
    }

    drawTickMarks(ctx, centerX, centerY, radius) {
        try {
            const tickCount = 12;
            const tickLength = 8;

            for (let i = 0; i < tickCount; i++) {
                const angle = (i / tickCount) * 2 * Math.PI - Math.PI / 2;
                const startX = centerX + Math.cos(angle) * (radius - tickLength);
                const startY = centerY + Math.sin(angle) * (radius - tickLength);
                const endX = centerX + Math.cos(angle) * radius;
                const endY = centerY + Math.sin(angle) * radius;

                ctx.beginPath();
                ctx.moveTo(startX, startY);
                ctx.lineTo(endX, endY);
                ctx.strokeStyle = '#BDBDBD';
                ctx.lineWidth = 2;
                ctx.stroke();
            }
        } catch (error) {
            console.error('Error drawing tick marks:', error);
        }
    }

    checkLowUnitsWarning() {
        const currentUnits = StorageManager.getCurrentUnits();
        const settings = StorageManager.getSettings();
        const threshold = settings.lowUnitsThreshold;
        
        const warningElement = document.getElementById('low-units-warning');
        
        if (warningElement) {
            if (currentUnits <= threshold) {
                warningElement.style.display = 'flex';
                warningElement.classList.add('warning-active');
                
                // Show notification if enabled
                if (settings.notifications.enabled && window.notificationManager) {
                    window.notificationManager.showLowUnitsNotification(currentUnits, threshold);
                }
            } else {
                warningElement.style.display = 'none';
                warningElement.classList.remove('warning-active');
            }
        }
    }

    // Public methods for external use
    updateUnits(newUnits) {
        StorageManager.setCurrentUnits(newUnits);
        this.refresh();
    }

    addPurchase(currency, units) {
        const currentUnits = StorageManager.getCurrentUnits();
        const newTotal = currentUnits + units;
        
        // Add purchase record
        StorageManager.addPurchase({
            currency: currency,
            units: units,
            previousUnits: currentUnits,
            newTotal: newTotal
        });
        
        // Update current units
        StorageManager.setCurrentUnits(newTotal);
        this.refresh();
    }

    recordUsage(newUnits) {
        const currentUnits = StorageManager.getCurrentUnits();
        const usage = currentUnits - newUnits;
        
        if (usage > 0) {
            // Add usage record
            StorageManager.addUsageRecord({
                previousUnits: currentUnits,
                currentUnits: newUnits,
                usage: usage
            });
            
            // Update current units
            StorageManager.setCurrentUnits(newUnits);
            this.refresh();
        }
    }

    destroy() {
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DashboardManager;
}
