// Notification Manager - <PERSON><PERSON> push notifications and reminders
class NotificationManager {
    constructor() {
        this.permission = 'default';
        this.scheduledNotifications = new Map();
        this.init();
    }

    async init() {
        // Check notification support
        if (!('Notification' in window)) {
            console.warn('This browser does not support notifications');
            return;
        }

        // Check current permission
        this.permission = Notification.permission;
        
        // Setup notification scheduling
        this.setupNotificationScheduling();
        
        console.log('Notification Manager initialized');
    }

    async requestPermission() {
        if (!('Notification' in window)) {
            return false;
        }

        if (this.permission === 'granted') {
            return true;
        }

        if (this.permission === 'denied') {
            this.showPermissionDeniedMessage();
            return false;
        }

        try {
            const permission = await Notification.requestPermission();
            this.permission = permission;
            
            if (permission === 'granted') {
                this.showMessage('Notifications enabled successfully!', 'success');
                return true;
            } else {
                this.showPermissionDeniedMessage();
                return false;
            }
        } catch (error) {
            console.error('Error requesting notification permission:', error);
            return false;
        }
    }

    showPermissionDeniedMessage() {
        this.showMessage('Notifications are disabled. You can enable them in your browser settings.', 'warning');
    }

    async showNotification(title, options = {}) {
        if (!('Notification' in window)) {
            return false;
        }

        if (this.permission !== 'granted') {
            const granted = await this.requestPermission();
            if (!granted) return false;
        }

        const defaultOptions = {
            icon: '/assets/icon-192.png',
            badge: '/assets/icon-192.png',
            tag: 'electricity-app',
            requireInteraction: false,
            silent: false
        };

        const notificationOptions = { ...defaultOptions, ...options };

        try {
            const notification = new Notification(title, notificationOptions);
            
            // Auto-close after 5 seconds if not requiring interaction
            if (!notificationOptions.requireInteraction) {
                setTimeout(() => {
                    notification.close();
                }, 5000);
            }

            // Handle notification click
            notification.onclick = () => {
                window.focus();
                notification.close();
                
                // Navigate to relevant page if specified
                if (options.action && window.electricityApp) {
                    this.handleNotificationAction(options.action);
                }
            };

            return notification;
        } catch (error) {
            console.error('Error showing notification:', error);
            return false;
        }
    }

    handleNotificationAction(action) {
        switch (action) {
            case 'record-usage':
                window.electricityApp.showPage('usage');
                break;
            case 'add-purchase':
                window.electricityApp.showPage('purchases');
                break;
            case 'view-dashboard':
                window.electricityApp.showPage('dashboard');
                break;
            default:
                window.electricityApp.showPage('dashboard');
        }
    }

    showLowUnitsNotification(currentUnits, threshold) {
        const settings = StorageManager.getSettings();
        
        if (!settings.notifications.enabled) {
            return;
        }

        const title = '⚠️ Low Electricity Units';
        const message = `You have ${currentUnits.toFixed(1)} ${settings.unit.name} remaining (below ${threshold} ${settings.unit.name} threshold)`;
        
        this.showNotification(title, {
            body: message,
            icon: '/assets/icon-192.png',
            tag: 'low-units-warning',
            requireInteraction: true,
            actions: [
                {
                    action: 'add-purchase',
                    title: 'Add Purchase'
                }
            ]
        });
    }

    showDailyReminder() {
        const settings = StorageManager.getSettings();
        
        if (!settings.notifications.enabled) {
            return;
        }

        const title = '📊 Daily Usage Reminder';
        const message = settings.notifications.message || 'Don\'t forget to record your electricity usage!';
        
        this.showNotification(title, {
            body: message,
            icon: '/assets/icon-192.png',
            tag: 'daily-reminder',
            requireInteraction: false,
            action: 'record-usage'
        });
    }

    setupNotificationScheduling() {
        // Clear existing scheduled notifications
        this.clearScheduledNotifications();
        
        const settings = StorageManager.getSettings();
        
        if (!settings.notifications.enabled) {
            return;
        }

        // Schedule daily reminder
        this.scheduleDailyReminder(settings.notifications.time);
    }

    scheduleDailyReminder(time) {
        if (!time || typeof time !== 'string') {
            console.warn('Invalid time format for daily reminder');
            return;
        }

        const [hours, minutes] = time.split(':').map(Number);

        if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
            console.warn('Invalid time values for daily reminder:', time);
            return;
        }

        const scheduleNextReminder = () => {
            const now = new Date();
            const scheduledTime = new Date();
            scheduledTime.setHours(hours, minutes, 0, 0);

            // If the time has already passed today, schedule for tomorrow
            if (scheduledTime <= now) {
                scheduledTime.setDate(scheduledTime.getDate() + 1);
            }

            const timeUntilReminder = scheduledTime.getTime() - now.getTime();

            // Don't schedule if time is too far in the future (more than 25 hours)
            if (timeUntilReminder > 25 * 60 * 60 * 1000) {
                console.warn('Reminder time too far in future, rescheduling...');
                setTimeout(scheduleNextReminder, 60000); // Try again in 1 minute
                return;
            }

            const timeoutId = setTimeout(() => {
                console.log('🔔 Triggering daily reminder notification...');
                this.showDailyReminder();

                // Schedule the next day's reminder
                setTimeout(scheduleNextReminder, 1000);
            }, timeUntilReminder);

            this.scheduledNotifications.set('daily-reminder', timeoutId);

            const timeString = scheduledTime.toLocaleString();
            console.log(`📅 Daily reminder scheduled for ${timeString} (in ${Math.round(timeUntilReminder / 1000 / 60)} minutes)`);
        };

        scheduleNextReminder();
    }

    clearScheduledNotifications() {
        this.scheduledNotifications.forEach((timeoutId) => {
            clearTimeout(timeoutId);
        });
        this.scheduledNotifications.clear();
    }

    updateSettings() {
        // Reschedule notifications when settings change
        this.setupNotificationScheduling();
    }

    // Test notification (for settings page)
    async testNotification() {
        const granted = await this.requestPermission();
        if (!granted) return false;

        return this.showNotification('🧪 Test Notification', {
            body: 'Notifications are working correctly!',
            tag: 'test-notification',
            requireInteraction: false
        });
    }

    // Show usage milestone notifications
    showUsageMilestone(milestone) {
        const settings = StorageManager.getSettings();
        
        if (!settings.notifications.enabled) {
            return;
        }

        let title, message;
        
        switch (milestone.type) {
            case 'weekly-goal':
                title = '🎯 Weekly Goal Achieved!';
                message = `You've used ${milestone.amount} ${settings.unit.name} this week`;
                break;
            case 'monthly-goal':
                title = '🏆 Monthly Goal Achieved!';
                message = `You've used ${milestone.amount} ${settings.unit.name} this month`;
                break;
            case 'savings':
                title = '💰 Great Savings!';
                message = `You've saved ${settings.currency.symbol}${milestone.amount} compared to last period`;
                break;
            default:
                return;
        }
        
        this.showNotification(title, {
            body: message,
            icon: '/assets/icon-192.png',
            tag: `milestone-${milestone.type}`,
            requireInteraction: false,
            action: 'view-dashboard'
        });
    }

    // Show purchase confirmation notification
    showPurchaseConfirmation(purchase) {
        const settings = StorageManager.getSettings();
        
        if (!settings.notifications.enabled) {
            return;
        }

        const title = '✅ Purchase Recorded';
        const message = `Added ${purchase.units} ${settings.unit.name} for ${settings.currency.symbol}${purchase.currency.toFixed(2)}`;
        
        this.showNotification(title, {
            body: message,
            icon: '/assets/icon-192.png',
            tag: 'purchase-confirmation',
            requireInteraction: false,
            action: 'view-dashboard'
        });
    }

    // Show usage recorded notification
    showUsageConfirmation(usage) {
        const settings = StorageManager.getSettings();
        
        if (!settings.notifications.enabled) {
            return;
        }

        const title = '📊 Usage Recorded';
        const message = `Recorded ${usage.amount} ${settings.unit.name} usage`;
        
        this.showNotification(title, {
            body: message,
            icon: '/assets/icon-192.png',
            tag: 'usage-confirmation',
            requireInteraction: false,
            action: 'view-dashboard'
        });
    }

    // Check if notifications are supported and enabled
    isSupported() {
        return 'Notification' in window;
    }

    isEnabled() {
        const settings = StorageManager.getSettings();
        return this.isSupported() && this.permission === 'granted' && settings.notifications.enabled;
    }

    getPermissionStatus() {
        return this.permission;
    }

    // Simple message display fallback
    showMessage(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
        
        // You could implement a toast notification system here
        if (window.electricityApp) {
            window.electricityApp.showNotification(message, type);
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotificationManager;
}
