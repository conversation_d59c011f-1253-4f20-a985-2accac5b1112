/* CSS Variables for Theming */
:root {
    /* Default Theme - Electric Blue */
    --primary-color: #2196F3;
    --primary-dark: #1976D2;
    --primary-light: #BBDEFB;
    --secondary-color: #FF9800;
    --accent-color: #4CAF50;
    --background-color: #F5F5F5;
    --surface-color: #FFFFFF;
    --text-primary: #212121;
    --text-secondary: #757575;
    --text-hint: #BDBDBD;
    --error-color: #F44336;
    --warning-color: #FF9800;
    --success-color: #4CAF50;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    --gradient-secondary: linear-gradient(135deg, var(--secondary-color), #F57C00);
    --gradient-success: linear-gradient(135deg, var(--success-color), #388E3C);
    --gradient-warning: linear-gradient(135deg, var(--warning-color), #F57C00);
    --gradient-error: linear-gradient(135deg, var(--error-color), #D32F2F);
    
    /* Shadows */
    --shadow-light: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 8px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 16px rgba(0,0,0,0.2);
    
    /* Border Radius */
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 24px;
    
    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    
    /* Typography */
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-md: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 24px;
    --font-size-xxl: 32px;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity var(--transition-medium);
}

.loading-content {
    text-align: center;
    color: white;
}

.lightning-logo {
    font-size: 4rem;
    margin-bottom: var(--spacing-md);
    animation: pulse 2s infinite;
}

.loading-content h2 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
}

.loading-content p {
    font-size: var(--font-size-md);
    opacity: 0.9;
    margin-bottom: var(--spacing-lg);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* App Container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.app-header {
    background: var(--gradient-primary);
    color: white;
    padding: var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-medium);
    position: sticky;
    top: 0;
    z-index: 100;
}

.menu-toggle {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: background-color var(--transition-fast);
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.menu-toggle:hover {
    background-color: rgba(255,255,255,0.1);
}

.menu-toggle span {
    width: 24px;
    height: 3px;
    background-color: white;
    border-radius: 2px;
    transition: var(--transition-fast);
}

.app-title {
    font-size: var(--font-size-lg);
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.lightning-icon {
    font-size: var(--font-size-xl);
}

.header-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.icon-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: background-color var(--transition-fast);
    font-size: var(--font-size-lg);
}

.icon-btn:hover {
    background-color: rgba(255,255,255,0.1);
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    left: -300px;
    width: 300px;
    height: 100vh;
    background: var(--surface-color);
    box-shadow: var(--shadow-heavy);
    transition: left var(--transition-medium);
    z-index: 200;
    overflow-y: auto;
}

.sidebar.open {
    left: 0;
}

.sidebar-header {
    background: var(--gradient-primary);
    color: white;
    padding: var(--spacing-lg);
    text-align: center;
}

.sidebar-header .logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
}

.sidebar-header .lightning-logo {
    font-size: 2rem;
}

.logo-text h3 {
    font-size: var(--font-size-lg);
    font-weight: 700;
}

.logo-text p {
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

.nav-menu {
    list-style: none;
    padding: var(--spacing-md) 0;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-primary);
    text-decoration: none;
    transition: background-color var(--transition-fast);
    font-weight: 500;
}

.nav-link:hover {
    background-color: var(--primary-light);
}

.nav-link.active {
    background: var(--gradient-primary);
    color: white;
}

.nav-icon {
    font-size: var(--font-size-lg);
}

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-medium);
    z-index: 150;
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: var(--spacing-lg);
    max-width: 100%;
    overflow-x: hidden;
}

/* Pages */
.page {
    display: none;
    animation: fadeIn var(--transition-medium);
}

.page.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.page-header {
    margin-bottom: var(--spacing-lg);
}

.page-header h2 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.page-icon {
    font-size: var(--font-size-xl);
}

/* Quick Actions */
.quick-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.quick-action-btn {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-md);
    font-weight: 600;
    cursor: pointer;
    transition: transform var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    box-shadow: var(--shadow-light);
    height: 50px;
}

.quick-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.action-icon {
    font-size: var(--font-size-lg);
}

/* Status Cards */
.status-cards {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.status-card {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    text-align: center;
    border: 3px solid var(--primary-light);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.card-icon {
    font-size: var(--font-size-lg);
}

.card-header h3 {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-secondary);
}

.card-value {
    font-size: var(--font-size-xxl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.card-unit {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* Usage Dial */
.dial-container {
    display: flex;
    justify-content: center;
    margin: var(--spacing-lg) 0;
}

.dial-wrapper {
    position: relative;
    width: 300px;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#usage-dial {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    box-shadow: var(--shadow-medium);
}

.dial-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    pointer-events: none;
}

.dial-value {
    font-size: var(--font-size-xxl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.dial-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 600;
}

/* Usage Summary */
.usage-summary {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.summary-card {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    text-align: center;
    border: 2px solid var(--primary-light);
}

.summary-card h4 {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.summary-icon {
    font-size: var(--font-size-md);
}

.summary-value {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.summary-cost {
    font-size: var(--font-size-md);
    color: var(--text-secondary);
    font-weight: 600;
}

/* Warning Card */
.warning-card {
    background: var(--gradient-warning);
    color: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    box-shadow: var(--shadow-medium);
    animation: warningPulse 2s infinite;
}

.warning-icon {
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.warning-content h4 {
    font-size: var(--font-size-lg);
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
}

.warning-content p {
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

@keyframes warningPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        padding: var(--spacing-md);
    }

    .status-cards {
        grid-template-columns: 1fr;
    }

    .usage-summary {
        grid-template-columns: 1fr;
    }

    .dial-wrapper {
        width: 250px;
        height: 250px;
    }

    .app-title {
        font-size: var(--font-size-md);
    }

    .lightning-icon {
        font-size: var(--font-size-lg);
    }
}

@media (max-width: 480px) {
    .app-header {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .main-content {
        padding: var(--spacing-sm);
    }

    .quick-action-btn {
        height: 45px;
        font-size: var(--font-size-sm);
    }

    .dial-wrapper {
        width: 200px;
        height: 200px;
    }

    .dial-value {
        font-size: var(--font-size-xl);
    }

    .warning-card {
        flex-direction: column;
        text-align: center;
    }
}

/* Purchases Page Styles */
.page-description {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

.purchase-form-container {
    margin-bottom: var(--spacing-xl);
}

.form-card {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    border: 2px solid var(--primary-light);
}

.form-card h3 {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.form-icon {
    font-size: var(--font-size-lg);
}

.input-group {
    margin-bottom: var(--spacing-lg);
}

.input-group label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.input-icon {
    font-size: var(--font-size-md);
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--background-color);
    border: 3px solid var(--primary-color);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    transition: border-color var(--transition-fast);
}

.input-wrapper:focus-within {
    border-color: var(--primary-dark);
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.currency-symbol,
.units-symbol {
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-md);
    font-weight: 600;
    font-size: var(--font-size-md);
    min-width: 50px;
    text-align: center;
}

.input-wrapper input {
    flex: 1;
    border: none;
    outline: none;
    padding: var(--spacing-md);
    font-size: var(--font-size-md);
    background: transparent;
    color: var(--text-primary);
}

.input-wrapper input::placeholder {
    color: var(--text-hint);
}

/* Calculation Preview */
.calculation-preview {
    background: var(--gradient-primary);
    color: white;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.calculation-preview h4 {
    font-size: var(--font-size-md);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.preview-icon {
    font-size: var(--font-size-md);
}

.preview-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.preview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-sm);
}

.preview-label {
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

.preview-value {
    font-weight: 700;
    font-size: var(--font-size-sm);
}

.preview-value.highlight {
    background: rgba(255, 255, 255, 0.2);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
}

.btn-primary,
.btn-secondary {
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-md);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    min-width: 120px;
    justify-content: center;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-light);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background: var(--background-color);
    color: var(--text-primary);
    border: 2px solid var(--text-hint);
}

.btn-secondary:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-icon {
    font-size: var(--font-size-sm);
}

/* Recent Purchases */
.recent-purchases {
    margin-top: var(--spacing-xl);
}

.recent-purchases h3 {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.section-icon {
    font-size: var(--font-size-lg);
}

.purchases-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.purchase-item {
    background: var(--surface-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 2px solid var(--primary-light);
    transition: transform var(--transition-fast);
}

.purchase-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.purchase-info {
    flex: 1;
}

.purchase-amount {
    font-size: var(--font-size-md);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.purchase-details {
    display: flex;
    gap: var(--spacing-md);
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.purchase-date,
.purchase-time,
.purchase-rate {
    background: var(--background-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
}

.delete-purchase-btn {
    background: var(--gradient-error);
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-sm);
    cursor: pointer;
    font-size: var(--font-size-sm);
    transition: transform var(--transition-fast);
}

.delete-purchase-btn:hover {
    transform: scale(1.1);
}

.empty-state {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    display: block;
}

.empty-state p {
    font-size: var(--font-size-md);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.empty-state small {
    font-size: var(--font-size-sm);
    opacity: 0.8;
}

/* Responsive adjustments for purchases */
@media (max-width: 768px) {
    .preview-grid {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .btn-primary,
    .btn-secondary {
        width: 100%;
    }

    .purchase-details {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
}

@media (max-width: 480px) {
    .purchase-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .delete-purchase-btn {
        align-self: flex-end;
    }
}

/* Usage Page Styles */
.current-status {
    background: var(--background-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    border: 2px solid var(--primary-light);
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.status-item:last-child {
    margin-bottom: 0;
}

.status-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 600;
}

.status-value {
    font-size: var(--font-size-md);
    color: var(--primary-color);
    font-weight: 700;
}

.input-help {
    font-size: var(--font-size-xs);
    color: var(--text-hint);
    margin-top: var(--spacing-xs);
    display: block;
}

/* Usage Calculation Preview */
.usage-preview {
    background: var(--gradient-success);
    color: white;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.calculation-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    flex-wrap: wrap;
}

.calculation-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-sm);
    min-width: 80px;
}

.calculation-item.result {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.calc-label {
    font-size: var(--font-size-xs);
    opacity: 0.9;
    display: block;
    margin-bottom: var(--spacing-xs);
}

.calc-value {
    font-size: var(--font-size-lg);
    font-weight: 700;
    display: block;
}

.calculation-operator {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: white;
}

.usage-cost {
    text-align: center;
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-sm);
}

.cost-label {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    margin-right: var(--spacing-sm);
}

.cost-value {
    font-size: var(--font-size-lg);
    font-weight: 700;
}

/* Chart Container */
.chart-container {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 2px solid var(--primary-light);
}

.chart-wrapper {
    position: relative;
    height: 300px;
    margin-bottom: var(--spacing-lg);
    background: var(--background-color);
    border-radius: var(--border-radius-md);
    overflow: hidden;
}

#usage-chart {
    width: 100%;
    height: 100%;
}

.chart-controls {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
}

.chart-period-btn {
    background: var(--background-color);
    color: var(--text-primary);
    border: 2px solid var(--text-hint);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.chart-period-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.chart-period-btn.active {
    background: var(--gradient-primary);
    color: white;
    border-color: var(--primary-color);
}

/* Usage Statistics */
.usage-stats {
    margin-bottom: var(--spacing-xl);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.stat-card {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    border: 2px solid var(--primary-light);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: transform var(--transition-fast);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.stat-icon {
    font-size: 2rem;
    background: var(--gradient-primary);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.stat-value {
    font-size: var(--font-size-lg);
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
}

.stat-cost {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 600;
}

/* Usage Records List */
.usage-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.usage-item {
    background: var(--surface-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 2px solid var(--primary-light);
    transition: transform var(--transition-fast);
}

.usage-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.usage-info {
    flex: 1;
}

.usage-amount {
    font-size: var(--font-size-md);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.usage-details {
    display: flex;
    gap: var(--spacing-md);
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.usage-date,
.usage-time,
.usage-reading {
    background: var(--background-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
}

.usage-cost {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--success-color);
    background: var(--gradient-success);
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
}

/* Responsive adjustments for usage page */
@media (max-width: 768px) {
    .calculation-display {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .calculation-operator {
        transform: rotate(90deg);
        font-size: var(--font-size-lg);
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
    }

    .chart-wrapper {
        height: 250px;
    }

    .usage-details {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
}

@media (max-width: 480px) {
    .usage-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .usage-cost {
        align-self: flex-end;
    }

    .chart-controls {
        flex-direction: column;
    }

    .chart-period-btn {
        width: 100%;
    }
}

/* History Page Styles */
.history-summary {
    margin-bottom: var(--spacing-xl);
}

.summary-card-large {
    background: var(--gradient-primary);
    color: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-medium);
}

.summary-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.summary-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin: 0;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.summary-stat {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    text-align: center;
}

.summary-stat .stat-label {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    margin-bottom: var(--spacing-sm);
    display: block;
}

.summary-stat .stat-value {
    font-size: var(--font-size-xl);
    font-weight: 700;
    display: block;
}

/* Period Analysis */
.period-analysis {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 2px solid var(--primary-light);
}

.period-tabs {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--background-color);
    padding-bottom: var(--spacing-md);
}

.period-tab {
    background: none;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.period-tab:hover {
    background: var(--background-color);
    color: var(--primary-color);
}

.period-tab.active {
    background: var(--gradient-primary);
    color: white;
}

.period-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.period-stat-card {
    background: var(--background-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    border: 2px solid var(--primary-light);
}

.period-stat-icon {
    font-size: 2rem;
    background: var(--gradient-primary);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.period-stat-content {
    flex: 1;
}

.period-stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.period-stat-value {
    font-size: var(--font-size-lg);
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
}

.period-stat-value.positive {
    color: var(--success-color);
}

.period-stat-value.negative {
    color: var(--error-color);
}

.period-stat-amount {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 600;
}

.period-stat-amount.positive {
    color: var(--success-color);
}

.period-stat-amount.negative {
    color: var(--error-color);
}

/* History Filters */
.history-filters {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 2px solid var(--primary-light);
}

.filter-controls {
    display: flex;
    gap: var(--spacing-lg);
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.filter-group label {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.filter-select {
    padding: var(--spacing-md);
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-md);
    background: var(--background-color);
    color: var(--text-primary);
    min-width: 150px;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-dark);
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

/* Timeline Styles */
.history-timeline {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 2px solid var(--primary-light);
}

.timeline-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    max-height: 500px;
    overflow-y: auto;
}

.timeline-item {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    transition: transform var(--transition-fast);
}

.timeline-item:hover {
    transform: translateX(5px);
}

.timeline-item.purchase {
    background: linear-gradient(90deg, rgba(76, 175, 80, 0.1), transparent);
    border-left: 4px solid var(--success-color);
}

.timeline-item.usage {
    background: linear-gradient(90deg, rgba(33, 150, 243, 0.1), transparent);
    border-left: 4px solid var(--primary-color);
}

.timeline-icon {
    font-size: var(--font-size-lg);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    background: var(--background-color);
    border: 2px solid var(--primary-light);
}

.timeline-content-inner {
    flex: 1;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.timeline-type {
    font-size: var(--font-size-sm);
    font-weight: 700;
    color: var(--primary-color);
}

.timeline-date {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    font-weight: 600;
}

.timeline-date.today {
    color: var(--success-color);
    font-weight: 700;
}

.timeline-details {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.timeline-time {
    font-size: var(--font-size-xs);
    color: var(--text-hint);
}

/* History Table */
.history-table-container {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    border: 2px solid var(--primary-light);
    overflow: hidden;
}

.table-wrapper {
    overflow-x: auto;
    margin: -var(--spacing-lg);
    padding: var(--spacing-lg);
}

.history-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--font-size-sm);
}

.history-table th {
    background: var(--gradient-primary);
    color: white;
    padding: var(--spacing-md);
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid var(--primary-dark);
}

.history-table td {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--background-color);
    vertical-align: top;
}

.table-row:hover {
    background: var(--background-color);
}

.purchase-row {
    border-left: 4px solid var(--success-color);
}

.usage-row {
    border-left: 4px solid var(--primary-color);
}

.date-cell {
    min-width: 120px;
}

.date-display {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.date-display .date {
    font-weight: 600;
    color: var(--text-primary);
}

.date-display .time {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.type-cell {
    min-width: 100px;
}

.type-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.type-badge.purchase {
    background: var(--gradient-success);
    color: white;
}

.type-badge.usage {
    background: var(--gradient-primary);
    color: white;
}

.details-cell {
    min-width: 200px;
}

.details-cell small {
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
}

.amount-cell {
    text-align: right;
    font-weight: 700;
    min-width: 100px;
}

.usage-amount {
    color: var(--primary-color);
}

.cost-cell {
    text-align: right;
    font-weight: 700;
    min-width: 80px;
}

.purchase-cost {
    color: var(--success-color);
}

.usage-cost {
    color: var(--primary-color);
}

.actions-cell {
    text-align: center;
    min-width: 60px;
}

.delete-btn {
    background: var(--gradient-error);
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs);
    cursor: pointer;
    font-size: var(--font-size-xs);
    transition: transform var(--transition-fast);
}

.delete-btn:hover {
    transform: scale(1.1);
}

.empty-table {
    text-align: center;
    padding: var(--spacing-xl);
}

/* Responsive adjustments for history page */
@media (max-width: 768px) {
    .summary-stats {
        grid-template-columns: 1fr 1fr;
    }

    .period-stats {
        grid-template-columns: 1fr;
    }

    .period-tabs {
        flex-direction: column;
    }

    .filter-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        width: 100%;
    }

    .filter-select {
        width: 100%;
    }

    .timeline-item {
        flex-direction: column;
        text-align: center;
    }

    .timeline-header {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .history-table {
        font-size: var(--font-size-xs);
    }

    .history-table th,
    .history-table td {
        padding: var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .summary-stats {
        grid-template-columns: 1fr;
    }

    .period-stat-card {
        flex-direction: column;
        text-align: center;
    }

    .timeline-content {
        max-height: 300px;
    }

    .table-wrapper {
        overflow-x: scroll;
    }

    .history-table {
        min-width: 600px;
    }
}

/* Settings Page Styles */
.settings-nav {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xl);
}

.settings-nav-btn {
    background: var(--surface-color);
    color: var(--text-primary);
    border: 2px solid var(--primary-light);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-md);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    height: 50px;
}

.settings-nav-btn:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

.settings-nav-btn.active {
    background: var(--gradient-primary);
    color: white;
    border-color: var(--primary-color);
}

.nav-btn-icon {
    font-size: var(--font-size-lg);
}

.settings-section {
    display: none;
}

.settings-section.active {
    display: block;
    animation: fadeIn var(--transition-medium);
}

.settings-card {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    border: 2px solid var(--primary-light);
}

.settings-card h3 {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.card-icon {
    font-size: var(--font-size-lg);
}

.setting-group {
    margin-bottom: var(--spacing-lg);
}

.setting-group:last-child {
    margin-bottom: 0;
}

.setting-group label {
    display: block;
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.setting-select,
.setting-input,
.setting-textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-md);
    background: var(--background-color);
    color: var(--text-primary);
    transition: border-color var(--transition-fast);
}

.setting-select:focus,
.setting-input:focus,
.setting-textarea:focus {
    outline: none;
    border-color: var(--primary-dark);
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.input-with-preview {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.input-with-preview input {
    flex: 1;
}

.input-preview {
    background: var(--gradient-primary);
    color: white;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    font-weight: 600;
    min-width: 100px;
    text-align: center;
}

.setting-help {
    font-size: var(--font-size-xs);
    color: var(--text-hint);
    margin-top: var(--spacing-xs);
    display: block;
}

/* Toggle Switch */
.toggle-label {
    display: flex !important;
    align-items: center;
    gap: var(--spacing-md);
    cursor: pointer;
    font-weight: 600;
}

.toggle-input {
    display: none;
}

.toggle-slider {
    position: relative;
    width: 60px;
    height: 30px;
    background: var(--text-hint);
    border-radius: 30px;
    transition: background var(--transition-fast);
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    width: 24px;
    height: 24px;
    background: white;
    border-radius: 50%;
    transition: transform var(--transition-fast);
}

.toggle-input:checked + .toggle-slider {
    background: var(--primary-color);
}

.toggle-input:checked + .toggle-slider::before {
    transform: translateX(30px);
}

/* Theme Grid */
.theme-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-md);
}

.theme-option {
    text-align: center;
    cursor: pointer;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    transition: all var(--transition-fast);
    border: 2px solid transparent;
}

.theme-option:hover {
    background: var(--background-color);
    transform: translateY(-2px);
}

.theme-option.active {
    border-color: var(--primary-color);
    background: var(--primary-light);
}

.theme-preview {
    width: 80px;
    height: 60px;
    border-radius: var(--border-radius-md);
    margin: 0 auto var(--spacing-sm);
    box-shadow: var(--shadow-light);
}

.theme-preview.electric-blue {
    background: linear-gradient(135deg, #2196F3, #1976D2);
}

.theme-preview.emerald-green {
    background: linear-gradient(135deg, #4CAF50, #388E3C);
}

.theme-preview.sunset-orange {
    background: linear-gradient(135deg, #FF9800, #F57C00);
}

.theme-preview.royal-purple {
    background: linear-gradient(135deg, #9C27B0, #7B1FA2);
}

.theme-preview.midnight-dark {
    background: linear-gradient(135deg, #424242, #212121);
}

.theme-name {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
}

/* Font Preview */
.font-preview {
    background: var(--background-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-md);
    border: 2px solid var(--primary-light);
}

.font-preview-small {
    font-size: 14px;
}

.font-preview-medium {
    font-size: 16px;
}

.font-preview-large {
    font-size: 18px;
}

/* Reset Options */
.danger-card {
    border-color: var(--error-color) !important;
}

.danger-card h3 {
    color: var(--error-color);
}

.reset-description {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.btn-warning {
    background: var(--gradient-warning);
    color: white;
    border: none;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-md);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    box-shadow: var(--shadow-light);
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-danger {
    background: var(--gradient-error);
    color: white;
    border: none;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-md);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    box-shadow: var(--shadow-light);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.data-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.file-input-label {
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* Color Scheme Variations */
.color-scheme-high-contrast {
    --text-primary: #000000;
    --text-secondary: #333333;
    --background-color: #FFFFFF;
    --surface-color: #F8F8F8;
}

.color-scheme-colorful {
    --primary-color: #E91E63;
    --secondary-color: #9C27B0;
    --accent-color: #FF5722;
    --success-color: #8BC34A;
}

.color-scheme-minimal {
    --primary-color: #607D8B;
    --secondary-color: #90A4AE;
    --accent-color: #546E7A;
    --background-color: #FAFAFA;
    --surface-color: #FFFFFF;
}

/* Responsive adjustments for settings page */
@media (max-width: 768px) {
    .input-with-preview {
        flex-direction: column;
        align-items: stretch;
    }

    .input-preview {
        text-align: left;
    }

    .theme-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .data-actions {
        flex-direction: column;
    }

    .toggle-label {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .theme-grid {
        grid-template-columns: 1fr;
    }

    .theme-preview {
        width: 60px;
        height: 40px;
    }

    .settings-nav-btn {
        height: auto;
        padding: var(--spacing-sm) var(--spacing-md);
    }
}
