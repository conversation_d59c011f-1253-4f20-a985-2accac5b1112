# Building the Prepaid Electricity Android APK

This guide explains how to build the Android APK for the Prepaid Electricity app.

## Prerequisites

1. **Android Studio** - Download and install from https://developer.android.com/studio
2. **Java Development Kit (JDK)** - JDK 8 or higher
3. **Android SDK** - Installed via Android Studio

## Setup Instructions

### 1. Install Android Studio
- Download Android Studio from the official website
- Install with default settings
- Open Android Studio and complete the setup wizard
- Install the latest Android SDK and build tools

### 2. Configure Environment Variables
Add these to your system environment variables:
```
ANDROID_HOME = C:\Users\<USER>\AppData\Local\Android\Sdk
JAVA_HOME = C:\Program Files\Java\jdk-[version]
```

Add to PATH:
```
%ANDROID_HOME%\platform-tools
%ANDROID_HOME%\tools
%JAVA_HOME%\bin
```

### 3. Open Project in Android Studio
1. Open Android Studio
2. Select "Open an existing Android Studio project"
3. Navigate to the `android` folder in this project
4. Click "OK" to open the project

### 4. Build the APK

#### Method 1: Using Android Studio GUI
1. In Android Studio, go to **Build** → **Build Bundle(s) / APK(s)** → **Build APK(s)**
2. Wait for the build to complete
3. Click "locate" in the notification to find the APK file
4. The APK will be in `android/app/build/outputs/apk/debug/app-debug.apk`

#### Method 2: Using Command Line
1. Open Command Prompt/PowerShell
2. Navigate to the `android` folder:
   ```cmd
   cd "C:\Users\<USER>\Documents\Augment-Projects\In Production Builds\Prepaid user\android"
   ```
3. Run the build command:
   ```cmd
   gradlew assembleDebug
   ```
4. The APK will be generated at `app/build/outputs/apk/debug/app-debug.apk`

#### Method 3: Release APK (for Play Store)
For a release APK suitable for Google Play Store:
```cmd
gradlew assembleRelease
```

## APK Location
After successful build, find your APK at:
- **Debug APK**: `android/app/build/outputs/apk/debug/app-debug.apk`
- **Release APK**: `android/app/build/outputs/apk/release/app-release.apk`

## Installing the APK

### On Android Device
1. Enable "Unknown Sources" in device settings
2. Transfer the APK file to your device
3. Tap the APK file to install

### Using ADB (Android Debug Bridge)
```cmd
adb install app-debug.apk
```

## Troubleshooting

### Common Issues

1. **Gradle Build Failed**
   - Ensure Android SDK is properly installed
   - Check internet connection for dependency downloads
   - Try: `gradlew clean` then `gradlew assembleDebug`

2. **Java/JDK Issues**
   - Verify JAVA_HOME is set correctly
   - Ensure JDK 8 or higher is installed

3. **Android SDK Issues**
   - Open Android Studio → SDK Manager
   - Install latest Android SDK Platform and Build Tools

4. **Permission Errors**
   - Run Command Prompt as Administrator
   - Check file permissions in project folder

### Build Logs
Check build logs in Android Studio's "Build" tab for detailed error information.

## App Features Included in APK

✅ **5 Complete Pages**:
- Dashboard with modern dial visualization
- Purchases tracking
- Usage monitoring  
- Transaction history
- Settings with 5 themes

✅ **Advanced Features**:
- Local data storage
- Push notifications
- Daily usage reminders
- Theme switching
- Responsive design

✅ **Android Integration**:
- Native notification system
- Background services
- WebView optimization
- Proper permissions

## Play Store Preparation

For Google Play Store submission:
1. Build release APK: `gradlew assembleRelease`
2. Sign the APK with your keystore
3. Test thoroughly on multiple devices
4. Prepare store listing materials
5. Follow Google Play Console guidelines

## Support

If you encounter issues:
1. Check Android Studio's event log
2. Review Gradle build output
3. Ensure all prerequisites are met
4. Try cleaning and rebuilding the project
